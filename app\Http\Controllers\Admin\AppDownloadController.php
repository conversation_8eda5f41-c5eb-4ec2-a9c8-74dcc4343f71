<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AppDownload;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class AppDownloadController extends Controller
{
    public function index()
    {
        $appDownload = AppDownload::active()->first();
        return view('admin.content.app-download.index', compact('appDownload'));
    }

    public function edit()
    {
        $appDownload = AppDownload::active()->first();
        if (!$appDownload) {
            $appDownload = new AppDownload();
        }
        return view('admin.content.app-download.edit', compact('appDownload'));
    }

    public function update(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'background_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'app_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'google_play_url' => 'nullable|url',
            'app_store_url' => 'nullable|url',
            'is_active' => 'boolean'
        ]);

        $appDownload = AppDownload::active()->first();
        $data = $request->all();
        
        if ($request->hasFile('background_image')) {
            if ($appDownload && $appDownload->background_image && Storage::disk('public')->exists($appDownload->background_image)) {
                Storage::disk('public')->delete($appDownload->background_image);
            }
            $data['background_image'] = $request->file('background_image')->store('app-download', 'public');
        }
        
        if ($request->hasFile('app_image')) {
            if ($appDownload && $appDownload->app_image && Storage::disk('public')->exists($appDownload->app_image)) {
                Storage::disk('public')->delete($appDownload->app_image);
            }
            $data['app_image'] = $request->file('app_image')->store('app-download', 'public');
        }

        if ($appDownload) {
            $appDownload->update($data);
        } else {
            AppDownload::create($data);
        }

        return redirect()->route('admin.app-download.index')
            ->with('success', 'App download section updated successfully.');
    }

    public function toggleStatus()
    {
        $appDownload = AppDownload::first();
        if ($appDownload) {
            $appDownload->update(['is_active' => !$appDownload->is_active]);
            
            return response()->json([
                'success' => true,
                'message' => 'Status updated successfully.',
                'is_active' => $appDownload->is_active
            ]);
        }
        
        return response()->json([
            'success' => false,
            'message' => 'App download section not found.'
        ]);
    }
}
