<?php

namespace App\Http\Controllers;

use App\Models\HeroSlider;
use App\Models\WhyChooseUs;
use App\Models\DailyOffer;
use App\Models\TeamMember;
use App\Models\AppDownload;
use App\Models\Counter;
use App\Models\Testimonial;
use App\Models\BlogPost;
use App\Models\BrandPartner;
use App\Models\Advertisement;
use App\Models\SectionSetting;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    public function index()
    {
        $data = [
            'heroSliders' => HeroSlider::active()->ordered()->get(),
            'whyChooseFeatures' => WhyChooseUs::active()->ordered()->get(),
            'dailyOffers' => DailyOffer::active()->ordered()->get(),
            'teamMembers' => TeamMember::active()->ordered()->get(),
            'appDownload' => AppDownload::active()->first(),
            'counters' => Counter::active()->ordered()->get(),
            'testimonials' => Testimonial::active()->ordered()->get(),
            'blogPosts' => BlogPost::active()->published()->ordered()->take(3)->get(),
            'brandPartners' => BrandPartner::active()->ordered()->get(),
            'advertisements' => Advertisement::active()->ordered()->get(),
            
            // Section Settings
            'bannerSection' => SectionSetting::getSection('banner'),
            'dailyOfferSection' => SectionSetting::getSection('daily_offer'),
            'menuSection' => SectionSetting::getSection('menu'),
            'teamSection' => SectionSetting::getSection('team'),
            'testimonialSection' => SectionSetting::getSection('testimonial'),
            'blogSection' => SectionSetting::getSection('blog'),
            'brandSection' => SectionSetting::getSection('brand'),
        ];

        return view('visitors.index', $data);
    }
}
