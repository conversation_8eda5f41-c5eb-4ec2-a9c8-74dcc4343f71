<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\WhyChooseUs;
use Illuminate\Http\Request;

class WhyChooseUsController extends Controller
{
    public function index()
    {
        $features = WhyChooseUs::ordered()->get();
        return view('admin.content.why-choose-us.index', compact('features'));
    }

    public function create()
    {
        return view('admin.content.why-choose-us.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'icon' => 'required|string|max:255',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0'
        ]);

        WhyChooseUs::create($request->all());

        return redirect()->route('admin.why-choose-us.index')
            ->with('success', 'Feature created successfully.');
    }

    public function show(WhyChooseUs $whyChooseUs)
    {
        return view('admin.content.why-choose-us.show', compact('whyChooseUs'));
    }

    public function edit(WhyChooseUs $whyChooseUs)
    {
        return view('admin.content.why-choose-us.edit', compact('whyChooseUs'));
    }

    public function update(Request $request, WhyChooseUs $whyChooseUs)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'icon' => 'required|string|max:255',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0'
        ]);

        $whyChooseUs->update($request->all());

        return redirect()->route('admin.why-choose-us.index')
            ->with('success', 'Feature updated successfully.');
    }

    public function destroy(WhyChooseUs $whyChooseUs)
    {
        $whyChooseUs->delete();

        return redirect()->route('admin.why-choose-us.index')
            ->with('success', 'Feature deleted successfully.');
    }

    public function toggleStatus(WhyChooseUs $whyChooseUs)
    {
        $whyChooseUs->update(['is_active' => !$whyChooseUs->is_active]);
        
        return response()->json([
            'success' => true,
            'message' => 'Status updated successfully.',
            'is_active' => $whyChooseUs->is_active
        ]);
    }
}
