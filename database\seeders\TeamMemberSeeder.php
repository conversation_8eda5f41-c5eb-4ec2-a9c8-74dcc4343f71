<?php

namespace Database\Seeders;

use App\Models\TeamMember;
use Illuminate\Database\Seeder;

class TeamMemberSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $members = [
            [
                'name' => 'Olivia Ava',
                'position' => 'Senior Chef',
                'image' => 'uploads/custom-images/olivia-ava-20230305042302.jpg',
                'facebook_url' => 'https://www.facebook.com/',
                'linkedin_url' => 'https://www.linkedin.com/',
                'twitter_url' => 'https://www.twitter.com/',
                'instagram_url' => 'https://www.instagram.com/',
                'is_active' => true,
                'sort_order' => 1
            ],
            [
                'name' => '<PERSON>',
                'position' => 'Senior Chef',
                'image' => 'uploads/custom-images/john-doe-20230305042351.jpg',
                'facebook_url' => 'https://www.facebook.com/',
                'linkedin_url' => 'https://www.linkedin.com/',
                'twitter_url' => 'https://www.twitter.com/',
                'instagram_url' => 'https://www.instagram.com/',
                'is_active' => true,
                'sort_order' => 2
            ],
            [
                'name' => 'Sophia Charle',
                'position' => 'Intern Chef',
                'image' => 'uploads/custom-images/sophia-charle-20230305042513.jpg',
                'facebook_url' => 'https://www.facebook.com/',
                'linkedin_url' => 'https://www.linkedin.com/',
                'twitter_url' => 'https://www.twitter.com/',
                'instagram_url' => 'https://www.instagram.com/',
                'is_active' => true,
                'sort_order' => 3
            ],
            [
                'name' => 'David Richard',
                'position' => 'Junior Chef',
                'image' => 'uploads/custom-images/david-richard-20230305042547.jpg',
                'facebook_url' => 'https://www.facebook.com/',
                'linkedin_url' => 'https://www.linkedin.com/',
                'twitter_url' => 'https://www.twitter.com/',
                'instagram_url' => 'https://www.instagram.com/',
                'is_active' => true,
                'sort_order' => 4
            ],
            [
                'name' => 'Flora Ocean',
                'position' => 'Web Developer',
                'image' => 'uploads/custom-images/flora-ocean-20230305042650.jpg',
                'facebook_url' => 'https://www.facebook.com/',
                'linkedin_url' => 'https://www.linkedin.com/',
                'twitter_url' => 'https://www.twitter.com/',
                'instagram_url' => 'https://www.instagram.com/',
                'is_active' => true,
                'sort_order' => 5
            ],
            [
                'name' => 'Freyja Mylah',
                'position' => 'Graphic Designer',
                'image' => 'uploads/custom-images/freyja-mylah-20230305042759.jpg',
                'facebook_url' => 'https://www.facebook.com/',
                'linkedin_url' => 'https://www.linkedin.com/',
                'twitter_url' => 'https://www.twitter.com/',
                'instagram_url' => 'https://www.instagram.com/',
                'is_active' => true,
                'sort_order' => 6
            ]
        ];

        foreach ($members as $member) {
            TeamMember::create($member);
        }
    }
}
