<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BrandPartner;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class BrandPartnerController extends Controller
{
    public function index()
    {
        $partners = BrandPartner::ordered()->get();
        return view('admin.content.brand-partners.index', compact('partners'));
    }

    public function create()
    {
        return view('admin.content.brand-partners.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'logo' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'url' => 'nullable|url',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0'
        ]);

        $data = $request->all();
        
        if ($request->hasFile('logo')) {
            $data['logo'] = $request->file('logo')->store('brand-partners', 'public');
        }

        BrandPartner::create($data);

        return redirect()->route('admin.brand-partners.index')
            ->with('success', 'Brand partner created successfully.');
    }

    public function show(BrandPartner $brandPartner)
    {
        return view('admin.content.brand-partners.show', compact('brandPartner'));
    }

    public function edit(BrandPartner $brandPartner)
    {
        return view('admin.content.brand-partners.edit', compact('brandPartner'));
    }

    public function update(Request $request, BrandPartner $brandPartner)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'url' => 'nullable|url',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0'
        ]);

        $data = $request->all();
        
        if ($request->hasFile('logo')) {
            // Delete old logo
            if ($brandPartner->logo && Storage::disk('public')->exists($brandPartner->logo)) {
                Storage::disk('public')->delete($brandPartner->logo);
            }
            $data['logo'] = $request->file('logo')->store('brand-partners', 'public');
        }

        $brandPartner->update($data);

        return redirect()->route('admin.brand-partners.index')
            ->with('success', 'Brand partner updated successfully.');
    }

    public function destroy(BrandPartner $brandPartner)
    {
        // Delete logo
        if ($brandPartner->logo && Storage::disk('public')->exists($brandPartner->logo)) {
            Storage::disk('public')->delete($brandPartner->logo);
        }

        $brandPartner->delete();

        return redirect()->route('admin.brand-partners.index')
            ->with('success', 'Brand partner deleted successfully.');
    }

    public function toggleStatus(BrandPartner $brandPartner)
    {
        $brandPartner->update(['is_active' => !$brandPartner->is_active]);
        
        return response()->json([
            'success' => true,
            'message' => 'Status updated successfully.',
            'is_active' => $brandPartner->is_active
        ]);
    }
}
