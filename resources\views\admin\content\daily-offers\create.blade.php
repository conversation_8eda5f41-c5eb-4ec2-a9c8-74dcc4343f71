@extends('admin.admin-layout')

@section('content')
<div class="row">
    <!-- Creative Header -->
    <div class="col-12">
        <div class="admin-creative-header mb-4">
            <div class="admin-header-content">
                <div class="admin-header-main">
                    <div class="admin-header-icon">
                        <i class="fas fa-plus-circle"></i>
                    </div>
                    <div class="admin-header-text">
                        <h1 class="admin-header-title">Create Daily Offer</h1>
                        <p class="admin-header-subtitle">Add a new special offer to attract customers</p>
                    </div>
                </div>
                <div class="admin-header-actions">
                    <a href="{{ route('admin.daily-offers.index') }}" class="admin-btn admin-btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="admin-form-wrapper">
            <div class="admin-form-header">
                <h3 class="admin-form-title">Offer Information</h3>
            </div>
            
            <form action="{{ route('admin.daily-offers.store') }}" method="POST" enctype="multipart/form-data">
                @csrf
                
                <div class="admin-form-group">
                    <label for="title" class="admin-form-label">Offer Title *</label>
                    <input type="text" class="admin-form-control @error('title') is-invalid @enderror" 
                           id="title" name="title" value="{{ old('title') }}" required>
                    @error('title')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="admin-form-group">
                    <label for="description" class="admin-form-label">Description *</label>
                    <textarea class="admin-form-control @error('description') is-invalid @enderror" 
                              id="description" name="description" rows="4" required>{{ old('description') }}</textarea>
                    @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="admin-form-group">
                            <label for="discount_percentage" class="admin-form-label">Discount Percentage *</label>
                            <div class="input-group">
                                <input type="number" class="admin-form-control @error('discount_percentage') is-invalid @enderror" 
                                       id="discount_percentage" name="discount_percentage" value="{{ old('discount_percentage') }}" 
                                       min="1" max="100" required>
                                <div class="input-group-append">
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                            @error('discount_percentage')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="admin-form-group">
                            <label for="sort_order" class="admin-form-label">Sort Order</label>
                            <input type="number" class="admin-form-control @error('sort_order') is-invalid @enderror" 
                                   id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" min="0">
                            @error('sort_order')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
                
                <div class="admin-form-group">
                    <label for="product_url" class="admin-form-label">Product URL</label>
                    <input type="text" class="admin-form-control @error('product_url') is-invalid @enderror" 
                           id="product_url" name="product_url" value="{{ old('product_url') }}" 
                           placeholder="Link to product page (optional)">
                    @error('product_url')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="admin-form-group">
                    <label for="image" class="admin-form-label">Offer Image *</label>
                    <div class="admin-file-upload">
                        <input type="file" class="admin-file-input @error('image') is-invalid @enderror" 
                               id="image" name="image" accept="image/*" required>
                        <label for="image" class="admin-file-label">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <span>Choose offer image</span>
                        </label>
                        <div class="admin-file-preview" id="imagePreview"></div>
                    </div>
                    @error('image')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="admin-form-actions">
                    <button type="submit" class="admin-btn admin-btn-primary">
                        <i class="fas fa-save"></i> Create Offer
                    </button>
                    <a href="{{ route('admin.daily-offers.index') }}" class="admin-btn admin-btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="admin-info-card">
            <div class="admin-info-header">
                <h4><i class="fas fa-info-circle"></i> Offer Guidelines</h4>
            </div>
            <div class="admin-info-content">
                <ul class="admin-info-list">
                    <li><strong>Image Size:</strong> Recommended 400x300px</li>
                    <li><strong>File Format:</strong> JPG, PNG, GIF</li>
                    <li><strong>Max File Size:</strong> 2MB</li>
                    <li><strong>Title:</strong> Keep it appetizing and clear</li>
                    <li><strong>Discount:</strong> 1-100% range</li>
                    <li><strong>Sort Order:</strong> Lower numbers appear first</li>
                </ul>
            </div>
        </div>
        
        <div class="admin-preview-card">
            <div class="admin-preview-header">
                <h4><i class="fas fa-eye"></i> Offer Preview</h4>
            </div>
            <div class="admin-preview-content">
                <div class="offer-preview">
                    <div class="preview-image">
                        <div id="preview-img" class="preview-placeholder">
                            <i class="fas fa-image"></i>
                            <span>Image Preview</span>
                        </div>
                        <div class="preview-discount">
                            <span id="preview-discount">0% OFF</span>
                        </div>
                    </div>
                    <div class="preview-content">
                        <h5 id="preview-title">Offer Title</h5>
                        <p id="preview-description">Offer description will appear here...</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="admin-status-card">
            <div class="admin-status-header">
                <h4><i class="fas fa-toggle-on"></i> Status Settings</h4>
            </div>
            <div class="admin-status-content">
                <div class="admin-form-group">
                    <div class="admin-checkbox-wrapper">
                        <input type="checkbox" id="is_active" name="is_active" value="1" 
                               {{ old('is_active', true) ? 'checked' : '' }}>
                        <label for="is_active" class="admin-checkbox-label">
                            <span class="admin-checkbox-text">Active</span>
                            <small class="admin-checkbox-help">Enable this offer to show on homepage</small>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.offer-preview {
    background: #f8f9fa;
    border-radius: 8px;
    overflow: hidden;
}

.preview-image {
    position: relative;
    height: 150px;
    background: #e9ecef;
}

.preview-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #6c757d;
}

.preview-placeholder i {
    font-size: 32px;
    margin-bottom: 8px;
}

.preview-discount {
    position: absolute;
    top: 10px;
    left: 10px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
}

.preview-content {
    padding: 15px;
}

.preview-content h5 {
    margin: 0 0 8px 0;
    color: var(--headingColor);
}

.preview-content p {
    margin: 0;
    color: var(--paraColor);
    font-size: 13px;
    line-height: 1.4;
}

#preview-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
</style>

<script>
// Live preview functionality
document.getElementById('title').addEventListener('input', function(e) {
    document.getElementById('preview-title').textContent = e.target.value || 'Offer Title';
});

document.getElementById('description').addEventListener('input', function(e) {
    document.getElementById('preview-description').textContent = e.target.value || 'Offer description will appear here...';
});

document.getElementById('discount_percentage').addEventListener('input', function(e) {
    document.getElementById('preview-discount').textContent = (e.target.value || '0') + '% OFF';
});

document.getElementById('image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const preview = document.getElementById('preview-img');
    
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.innerHTML = `<img src="${e.target.result}" alt="Preview">`;
        };
        reader.readAsDataURL(file);
    } else {
        preview.innerHTML = `
            <i class="fas fa-image"></i>
            <span>Image Preview</span>
        `;
    }
});
</script>
@endsection
