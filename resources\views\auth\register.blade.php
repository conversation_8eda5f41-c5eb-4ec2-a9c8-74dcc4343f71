<!DOCTYPE html>
<html lang="en">



<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />

        <title>Register</title>
        <meta name="description" content="Register">

    <link rel="icon" type="image/png" href="{{ asset('uploads/website-images/favicon-2023-03-05-01-07-01-8002.png') }}">
    <link rel="stylesheet" href="{{ asset('user/css/all.min.css') }}">
    <link rel="stylesheet" href="{{ asset('user/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ asset('user/css/spacing.css') }}">
    <link rel="stylesheet" href="{{ asset('user/css/slick.css') }}">
    <link rel="stylesheet" href="{{ asset('user/css/nice-select.css') }}">
    <link rel="stylesheet" href="{{ asset('user/css/venobox.min.css') }}">
    <link rel="stylesheet" href="{{ asset('user/css/animate.css') }}">
    <link rel="stylesheet" href="{{ asset('user/css/jquery.exzoom.css') }}">
    <link rel="stylesheet" href="{{ asset('toastr/toastr.min.css') }}">
    <link rel="stylesheet" href="{{ asset('user/css/style.css') }}">
    <link rel="stylesheet" href="{{ asset('user/css/responsive.css') }}">
    <link rel="stylesheet" href="{{ asset('backend/css/bootstrap-datepicker.min.css') }}">
    <link rel="stylesheet" href="{{ asset('backend/css/select2.min.css') }}">


    <style>
    :root {
    --colorPrimary: #eb0029;
    --paraColor: #494949;
    --colorBlack: #010f1c;
    --colorWhite: #ffffff;
    --paraFont: 'Barlow', sans-serif;
    --headingFont: 'Jost', sans-serif;
    --cursiveFont: 'Lobster', cursive;
    --boxShadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
    --gradiantBg: rgb(156, 3, 30)linear-gradient(0deg, rgba(156, 3, 30, 1) 0%, rgba(235, 0, 41, 1) 100%);
    --gradiantHoverBg: rgb(235, 0, 41)linear-gradient(0deg, rgba(235, 0, 41, 1) 0%, rgba(156, 3, 30, 1) 100%);
}


.footer_overlay{
    background: #b90424fa !important;
}


.topbar_icon li a {
    background: #ca0628 !important;
}

/* Custom styles for validation errors */
.text-danger {
    color: #eb0029 !important;
    font-size: 14px;
    font-weight: 500;
    margin-top: 5px;
}

.wsus__login_imput .text-danger {
    display: block;
    margin-top: 5px;
}

</style>

    <!--jquery library js-->
    <script src="{{ asset('user/js/jquery-3.6.3.min.js') }}"></script>
    <script src="{{ asset('user/js/<EMAIL>') }}"></script>
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>


        <script async src="https://www.googletagmanager.com/gtag/js?id=5248-fd-5fds"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '5248-fd-5fds');
    </script>

</head>

<body>

    <div class="" id="preloader">
        <div class="img d-none">
            <img src="{{ asset('uploads/website-images/Spinner.gif') }}" alt="UniFood" class="img-fluid">
        </div>
    </div>

    <!--=============================
        TOPBAR START
    ==============================-->
@include('visitors.includes.topbar')
    <!--=============================
        TOPBAR END
    ==============================-->


    <!--=============================
        MENU START
    ==============================-->
   @include('visitors.includes.nav')
    <!--=============================
        MENU END
    ==============================-->


    <!--=============================
        BREADCRUMB START
    ==============================-->
    <section class="wsus__breadcrumb" style="background: url({{ asset('uploads/website-images/breadcrumb_image-2022-12-31-01-18-17-5423.jpg') }});">
        <div class="wsus__breadcrumb_overlay">
            <div class="container">
                <div class="wsus__breadcrumb_text">
                    <h1>Register</h1>
                    <ul>
                        <li><a href="{{ route('home') }}">home</a></li>
                        <li><a href="{{ route('register') }}">Register</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </section>
    <!--=============================
        BREADCRUMB END
    ==============================-->

    <!--=========================
        SIGNUP START
    ==========================-->
    <section class="wsus__signin" style="background: url({{ asset('uploads/website-images/banner-2023-02-23-03-56-18-6177.jpg') }});">
        <div class="wsus__signin_overlay pt_125 xs_pt_95 pb_100 xs_pb_70">
            <div class="container">
                <div class="row wow fadeInUp" data-wow-duration="1s">
                    <div class="col-xxl-5 col-xl-6 col-md-9 col-lg-7 m-auto">
                        <div class="wsus__login_area">
                            <h2>Create Account!</h2>
                            <p>sign up to get started</p>
                            <form action="{{ route('register') }}" method="POST">
                                @csrf
                                <div class="row">
                                    <div class="col-xl-12">
                                        <div class="wsus__login_imput">
                                            <label>Name</label>
                                            <input type="text" name="name" placeholder="Full Name" value="{{ old('name') }}" required>
                                            @error('name')
                                                <div class="text-danger mt-1">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-xl-12">
                                        <div class="wsus__login_imput">
                                            <label>Email</label>
                                            <input type="email" name="email" placeholder="Email" value="{{ old('email') }}" required>
                                            @error('email')
                                                <div class="text-danger mt-1">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-xl-12">
                                        <div class="wsus__login_imput">
                                            <label>Password</label>
                                            <input type="password" name="password" placeholder="Password" required>
                                            @error('password')
                                                <div class="text-danger mt-1">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-xl-12">
                                        <div class="wsus__login_imput">
                                            <label>Confirm Password</label>
                                            <input type="password" name="password_confirmation" placeholder="Confirm Password" required>
                                            @error('password_confirmation')
                                                <div class="text-danger mt-1">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="col-xl-12">
                                        <div class="wsus__login_imput">
                                            <button type="submit" class="common_btn">Register</button>
                                        </div>
                                    </div>
                                </div>
                            </form>

                            <p class="create_account">Already have an account ? <a href="{{ route('login') }}">Login</a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!--=========================
        SIGNUP END
    ==========================-->



    <!--=============================
        BRAND START
    ==============================-->
    <section class="wsus__brand" style="background: url({{ asset('uploads/website-images/counter-bg-2023-03-06-09-34-36-3312.jpg') }});">
        <div class="wsus__brand_overlay">
            <div class="container">
                <div class="row brand_slider wow fadeInUp" data-wow-duration="1s">
                                            <div class="col-xl-2">
                                                        <a class="wsus__single_brand" href="javascript:;">
                                <img src="{{ asset('uploads/custom-images/partner-2023-03-05-04-35-57-4275.png') }}" alt="brand" class="img-fluid w-100">
                            </a>

                        </div>
                                            <div class="col-xl-2">
                                                        <a class="wsus__single_brand" href="javascript:;">
                                <img src="{{ asset('uploads/custom-images/partner-2023-03-05-04-36-05-2512.png') }}" alt="brand" class="img-fluid w-100">
                            </a>

                        </div>
                                            <div class="col-xl-2">
                                                        <a class="wsus__single_brand" href="javascript:;">
                                <img src="{{ asset('uploads/custom-images/partner-2023-03-05-04-36-15-7213.png') }}" alt="brand" class="img-fluid w-100">
                            </a>

                        </div>
                                            <div class="col-xl-2">
                                                        <a class="wsus__single_brand" href="javascript:;">
                                <img src="{{ asset('uploads/custom-images/partner-2023-03-05-04-36-24-3552.png') }}" alt="brand" class="img-fluid w-100">
                            </a>

                        </div>
                                            <div class="col-xl-2">
                                                        <a class="wsus__single_brand" href="javascript:;">
                                <img src="{{ asset('uploads/custom-images/partner-2023-03-05-04-36-34-1671.png') }}" alt="brand" class="img-fluid w-100">
                            </a>

                        </div>
                                            <div class="col-xl-2">
                                                        <a class="wsus__single_brand" href="javascript:;">
                                <img src="{{ asset('uploads/custom-images/partner-2023-03-05-04-36-42-1713.png') }}" alt="brand" class="img-fluid w-100">
                            </a>

                        </div>
                                    </div>
            </div>
        </div>
    </section>
    <!--=============================
        BRAND END
    ==============================-->


    <!--=============================
        FOOTER START
    ==============================-->
    @include('visitors.includes.footer')
    <!--=============================
        FOOTER END
    ==============================-->


    <!--=============================
        SCROLL BUTTON START
    ==============================-->
    <div class="wsus__scroll_btn">
        <i class="fas fa-chevron-up"></i>
    </div>
    <!--=============================
        SCROLL BUTTON  END
    ==============================-->


    <!--bootstrap js-->
    <script src="{{ asset('user/js/bootstrap.bundle.min.js') }}"></script>
    <!--font-awesome js-->
    <script src="{{ asset('user/js/Font-Awesome.js') }}"></script>
    <!-- slick slider -->
    <script src="{{ asset('user/js/slick.min.js') }}"></script>
    <!-- isotop js -->
    <script src="{{ asset('user/js/isotope.pkgd.min.js') }}"></script>
    <!-- simplyCountdownjs -->
    <script src="{{ asset('user/js/simplyCountdown.js') }}"></script>
    <!-- counter up js -->
    <script src="{{ asset('user/js/jquery.waypoints.min.js') }}"></script>
    <script src="{{ asset('user/js/jquery.countup.min.js') }}"></script>
    <!-- nice select js -->
    <script src="{{ asset('user/js/jquery.nice-select.min.js') }}"></script>
    <!-- venobox js -->
    <script src="{{ asset('user/js/venobox.min.js') }}"></script>
    <!-- sticky sidebar js -->
    <script src="{{ asset('user/js/sticky_sidebar.js') }}"></script>
    <!-- wow js -->
    <script src="{{ asset('user/js/wow.min.js') }}"></script>
    <!-- ex zoom js -->
    <script src="{{ asset('user/js/jquery.exzoom.js') }}"></script>

    <script src="{{ asset('backend/js/bootstrap-datepicker.min.js') }}"></script>

    <!--main/custom js-->
    <script src="{{ asset('user/js/main.js') }}"></script>

    <script src="{{ asset('toastr/toastr.min.js') }}"></script>
    <script src="{{ asset('backend/js/select2.min.js') }}"></script>

    <script>
            </script>

    <script>
    (function($) {
        "use strict";
        $(document).ready(function () {
            $("#subscribe_form").on('submit', function(e){
                e.preventDefault();
                if($("#subscribe_email").val() == ''){
                    toastr.error("Email is required")
                    return;
                }

                $("#subscribe_btn").prop("disabled", true);
                $("#subscribe_btn").html(`<i class="fas fa-spinner"></i>`);

                $.ajax({
                    type: 'POST',
                    data: $('#subscribe_form').serialize(),
                    url: "{{ url('/subscribe-request') }}",
                    success: function (response) {
                        toastr.success(response.message)
                        $("#subscribe_form").trigger("reset");
                        $("#subscribe_btn").prop("disabled", false);
                        $("#subscribe_btn").html(`<i class="fas fa-paper-plane"></i>`);
                    },
                    error: function(response) {
                        $("#subscribe_btn").prop("disabled", false);
                        $("#subscribe_btn").html(`<i class="fas fa-paper-plane"></i>`);

                        if(response.status == 403){
                            if(response.responseJSON.message)toastr.error(response.responseJSON.message)
                        }
                    }
                });
            })
        });
    })(jQuery);

    function remove_cart_item(rowid){
        $.ajax({
            type: 'get',
            url: "{{ url('/remove-cart-item') }}" + "/" + rowid,
            success: function (response) {
                toastr.success(response.message);

                let ready_to_reload = "no"
                if(ready_to_reload == 'yes'){
                    window.location.reload();
                }else{
                    $("#cart_item_" + rowid).remove();
                    $("#cart_total_sidebar").text("$" + response.total);
                    $("#cart_qty_sidebar").text(response.qty);
                    $("#cart_total_topbar").text("$" + response.total);
                    $("#cart_qty_topbar").text(response.qty);
                }

                calculate_mini_total();

            },
            error: function(response) {
                if(response.status == 500){
                    toastr.error(response.responseJSON.message)
                }
            }
        });
    }

    function calculate_mini_total(){
        let total = 0;
        $('.mini_cart_item').each(function(){
            let item_total = $(this).find('.item_total').text();
            item_total = item_total.replace('$', '');
            total += parseFloat(item_total);
        });
        $('#mini_cart_total').text('$' + total.toFixed(2));
    }

    function load_product_modal(product_id){
        $("#preloader").addClass('preloader')
        $(".img").removeClass('d-none')

        $.ajax({
            type: 'get',
            url: "{{ url('/load-product-modal') }}" + "/" + product_id,
            success: function (response) {
                $("#preloader").removeClass('preloader')
                $(".img").addClass('d-none')
                $(".load_product_modal_response").html(response)
                $("#cartModal").modal('show');
            },
            error: function(response) {
                $("#preloader").removeClass('preloader')
                $(".img").addClass('d-none')
            }
        });
    }

    function add_to_wishlist(id){
        $.ajax({
            type: 'get',
            url: "{{ url('/add-to-wishlist') }}" + "/" + id,
            success: function (response) {
                toastr.success("Wishlist added successfully");
            },
            error: function(response) {
                if(response.status == 500){
                    toastr.error(response.responseJSON.message)
                }else{
                    toastr.error(response.responseJSON.message)
                }
            }
        });
    }
    function before_auth_wishlist(){
        toastr.error("Please login first")
    }

</script>

</body>

</html>
