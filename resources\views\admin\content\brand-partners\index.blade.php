@extends('admin.admin-layout')

@section('content')
<div class="row">
    <!-- Creative Header -->
    <div class="col-12">
        <div class="admin-creative-header mb-4">
            <div class="admin-header-content">
                <div class="admin-header-main">
                    <div class="admin-header-icon">
                        <i class="fas fa-handshake"></i>
                    </div>
                    <div class="admin-header-text">
                        <h1 class="admin-header-title">Brand Partners</h1>
                        <p class="admin-header-subtitle">Manage your business partners and brand collaborations</p>
                    </div>
                </div>
                <div class="admin-header-actions">
                    <a href="{{ route('admin.brand-partners.create') }}" class="admin-btn">
                        <i class="fas fa-plus"></i> Add New Partner
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="admin-table-wrapper">
            <div class="admin-table-header">
                <h3 class="admin-table-title">All Brand Partners</h3>
                <div class="admin-table-actions">
                    <span class="admin-badge admin-badge-info">{{ $partners->count() }} Total</span>
                </div>
            </div>
            
            @if($partners->count() > 0)
                <div class="row">
                    @foreach($partners as $partner)
                    <div class="col-lg-4 col-xl-3 mb-4">
                        <div class="admin-partner-card">
                            <div class="admin-partner-logo">
                                <img src="{{ asset('storage/' . $partner->logo) }}" alt="{{ $partner->name }}">
                                <div class="admin-partner-status">
                                    <div class="admin-toggle-switch">
                                        <input type="checkbox" id="status-{{ $partner->id }}" 
                                               {{ $partner->is_active ? 'checked' : '' }}
                                               onchange="toggleStatus({{ $partner->id }}, '{{ route('admin.brand-partners.toggle-status', $partner) }}')">
                                        <label for="status-{{ $partner->id }}"></label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="admin-partner-content">
                                <h4 class="admin-partner-name">{{ $partner->name }}</h4>
                                
                                @if($partner->url)
                                    <div class="admin-partner-url">
                                        <a href="{{ $partner->url }}" target="_blank" class="admin-link">
                                            <i class="fas fa-external-link-alt"></i>
                                            Visit Website
                                        </a>
                                    </div>
                                @endif
                                
                                <div class="admin-partner-meta">
                                    <span class="admin-badge admin-badge-secondary">Order: {{ $partner->sort_order }}</span>
                                </div>
                                
                                <div class="admin-partner-actions">
                                    <a href="{{ route('admin.brand-partners.show', $partner) }}" class="admin-btn admin-btn-sm admin-btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.brand-partners.edit', $partner) }}" class="admin-btn admin-btn-sm admin-btn-warning">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('admin.brand-partners.destroy', $partner) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="admin-btn admin-btn-sm admin-btn-danger" 
                                                onclick="return confirm('Are you sure you want to delete this partner?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            @else
                <div class="admin-empty-state">
                    <div class="admin-empty-icon">
                        <i class="fas fa-handshake"></i>
                    </div>
                    <h3>No Brand Partners Found</h3>
                    <p>Add your first brand partner to showcase your business collaborations.</p>
                    <a href="{{ route('admin.brand-partners.create') }}" class="admin-btn">
                        <i class="fas fa-plus"></i> Add First Partner
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<style>
.admin-partner-card {
    background: var(--cardBg);
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    text-align: center;
    height: 100%;
}

.admin-partner-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.admin-partner-logo {
    position: relative;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
}

.admin-partner-logo img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    filter: grayscale(100%);
    transition: filter 0.3s ease;
}

.admin-partner-card:hover .admin-partner-logo img {
    filter: grayscale(0%);
}

.admin-partner-status {
    position: absolute;
    top: 10px;
    right: 10px;
}

.admin-partner-name {
    font-size: 18px;
    font-weight: 600;
    color: var(--headingColor);
    margin-bottom: 15px;
}

.admin-partner-url {
    margin-bottom: 15px;
}

.admin-link {
    color: var(--primaryColor);
    text-decoration: none;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    transition: color 0.3s ease;
}

.admin-link:hover {
    color: #ff6b6b;
    text-decoration: none;
}

.admin-partner-meta {
    margin-bottom: 20px;
}

.admin-partner-actions {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.admin-partner-actions .admin-btn {
    flex: 1;
    max-width: 45px;
}
</style>

<script>
function toggleStatus(id, url) {
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}
</script>
@endsection
