@import url('https://fonts.googleapis.com/css2?family=Barlow:ital,wght@0,300;0,400;0,500;0,600;0,700;1,800&amp;family=Jost:wght@200;300;400;500;600;700;800;900&amp;family=Lobster&amp;display=swap');

* {
    margin: 0;
    padding: 0;
}

ul,
ol,
li {
    list-style: none;
    margin: 0;
    padding: 0;
}

a {
    display: inline-block;
    text-decoration: none;
}

a:hover {
    text-decoration: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    margin: 0;
    padding: 0;
    font-family: var(--headingFont);
    color: var(--colorBlack);
}

p,
a,
span {
    font-size: 16px;
    font-weight: 400;
    margin: 0;
    color: var(--paraColor);
}

body {
    overflow-x: hidden;
    scroll-behavior: smooth;
    color: var(--paraColor);
    font-family: var(--paraFont);
}

img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
}

input,
textarea {
    width: 100%;
    padding: 12px 20px;
    outline: none;
    resize: none;
    border: 1px solid #eee;
    border-radius: 3px;
    font-size: 16px;
    font-weight: 300;
}

input::placeholder,
textarea::placeholder {
    color: #a6a6ac;
}

button {
    border: none;
    box-shadow: none !important;
}


/*============================
    ADMIN DASHBOARD CSS START
============================*/

/* Admin Dashboard Layout */
.admin-dashboard-content {
    min-height: 100vh;
    background: #f8f9fa;
    margin-left: 280px;
    transition: margin-left 0.3s ease;
}

.admin-dashboard-wrapper {
    position: relative;
    min-height: 100vh;
}

/* Admin Sidebar */
.admin-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 280px;
    height: 100vh;
    background: var(--colorWhite);
    border-right: 1px solid #e9ecef;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    z-index: 1000;
    overflow-y: auto;
    transition: transform 0.3s ease;
}

.admin-sidebar::-webkit-scrollbar {
    width: 6px;
}

.admin-sidebar::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.admin-sidebar::-webkit-scrollbar-thumb {
    background: var(--colorPrimary);
    border-radius: 3px;
}

/* Sidebar Header */
.admin-sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.admin-sidebar-brand {
    display: flex;
    align-items: center;
    gap: 12px;
}

.admin-sidebar-logo {
    width: 40px;
    height: 40px;
    object-fit: contain;
}

.admin-sidebar-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--colorBlack);
    font-family: var(--headingFont);
}

.admin-sidebar-close {
    background: none;
    border: none;
    font-size: 18px;
    color: var(--paraColor);
    cursor: pointer;
    padding: 5px;
}

/* Sidebar User Section */
.admin-sidebar-user {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 12px;
}

.admin-user-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: var(--gradiantBg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--colorWhite);
    font-size: 20px;
}

.admin-user-info {
    flex: 1;
}

.admin-user-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--colorBlack);
    margin: 0 0 2px 0;
    font-family: var(--headingFont);
}

.admin-user-role {
    font-size: 12px;
    color: var(--paraColor);
}

.admin-user-dropdown-btn {
    background: none;
    border: none;
    color: var(--paraColor);
    font-size: 12px;
    cursor: pointer;
    padding: 5px;
}

.admin-user-dropdown {
    border: none;
    box-shadow: var(--boxShadow);
    border-radius: 8px;
    overflow: hidden;
    min-width: 180px;
}

.admin-user-dropdown .dropdown-item {
    padding: 10px 15px;
    font-size: 13px;
    color: var(--paraColor);
    transition: all 0.3s ease;
}

.admin-user-dropdown .dropdown-item:hover {
    background: var(--colorPrimary);
    color: var(--colorWhite);
}

.admin-user-dropdown .dropdown-item i {
    margin-right: 8px;
    width: 14px;
}

/* Sidebar Notifications */
.admin-sidebar-notifications {
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
}

.admin-notification-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    background: rgba(235, 0, 41, 0.1);
    border-radius: 6px;
    border-left: 3px solid var(--colorPrimary);
}

.admin-notification-item i {
    color: var(--colorPrimary);
    font-size: 14px;
}

.admin-notification-text {
    flex: 1;
    font-size: 13px;
    color: var(--colorBlack);
    font-weight: 500;
}

.admin-notification-badge {
    background: var(--colorPrimary);
    color: var(--colorWhite);
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

/* Sidebar Navigation */
.admin-sidebar-nav {
    padding: 10px 0;
}

.admin-nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.admin-nav-item {
    margin: 0;
}

.admin-nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--paraColor);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.admin-nav-link:hover {
    background: rgba(235, 0, 41, 0.1);
    color: var(--colorPrimary);
    text-decoration: none;
}

.admin-nav-link.active {
    background: var(--gradiantBg);
    color: var(--colorWhite);
}

.admin-nav-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--colorWhite);
}

.admin-nav-icon {
    width: 18px;
    margin-right: 12px;
    font-size: 16px;
    text-align: center;
}

.admin-nav-text {
    flex: 1;
    font-size: 14px;
    font-weight: 500;
}

.admin-nav-arrow {
    font-size: 12px;
    transition: transform 0.3s ease;
}

/* Dropdown Navigation */
.admin-nav-dropdown.active .admin-nav-arrow {
    transform: rotate(90deg);
}

.admin-nav-submenu {
    list-style: none;
    margin: 0;
    padding: 0;
    background: #f8f9fa;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.admin-nav-dropdown.active .admin-nav-submenu {
    max-height: 300px;
}

.admin-nav-sublink {
    display: flex;
    align-items: center;
    padding: 10px 20px 10px 50px;
    color: var(--paraColor);
    text-decoration: none;
    font-size: 13px;
    transition: all 0.3s ease;
}

.admin-nav-sublink:hover {
    background: rgba(235, 0, 41, 0.1);
    color: var(--colorPrimary);
    text-decoration: none;
}

.admin-nav-sublink i {
    margin-right: 8px;
    font-size: 12px;
    width: 14px;
}

/* Sidebar Footer */
.admin-sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    background: var(--colorWhite);
}

.admin-sidebar-footer-item {
    margin-bottom: 8px;
}

.admin-footer-link {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--paraColor);
    text-decoration: none;
    font-size: 13px;
    padding: 6px 0;
    transition: color 0.3s ease;
}

.admin-footer-link:hover {
    color: var(--colorPrimary);
    text-decoration: none;
}

.admin-footer-link i {
    font-size: 12px;
    width: 14px;
}

/* Mobile Toggle */
.admin-mobile-toggle {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1001;
    background: var(--colorWhite);
    border-radius: 8px;
    box-shadow: var(--boxShadow);
}

.admin-sidebar-toggle {
    background: none;
    border: none;
    padding: 12px;
    color: var(--colorPrimary);
    font-size: 18px;
    cursor: pointer;
}

/* Sidebar Overlay */
.admin-sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.admin-sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Mobile Responsive */
@media (max-width: 991.98px) {
    .admin-sidebar {
        transform: translateX(-100%);
    }

    .admin-sidebar.active {
        transform: translateX(0);
    }

    .admin-dashboard-content {
        margin-left: 0;
    }

    .admin-mobile-toggle {
        display: block;
    }
}

@media (min-width: 992px) {
    .admin-mobile-toggle {
        display: none;
    }

    .admin-sidebar-overlay {
        display: none;
    }
}

/* Admin Topbar - Adjusted for Floating Sidebar */
.admin-topbar {
    background: var(--colorBlack);
    padding: 8px 0;
    margin-left: 360px; /* 300px sidebar + 40px spacing + 20px margin */
    transition: margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@media (max-width: 991.98px) {
    .admin-topbar {
        margin-left: 0;
        padding-left: 70px;
    }
}

.admin-topbar .wsus__topbar_info li a {
    color: var(--colorWhite);
    font-size: 13px;
}

.admin-topbar .wsus__topbar_info li a i {
    color: var(--colorPrimary);
    margin-right: 5px;
}

.admin-quick-stats {
    margin-right: 20px;
}

.admin-quick-stat {
    color: var(--colorWhite);
    font-size: 13px;
    font-weight: 500;
}

.admin-quick-stat i {
    color: var(--colorPrimary);
    margin-right: 5px;
}

.admin-quick-stat strong {
    color: var(--colorPrimary);
}

.admin-header-section {
    background: var(--colorWhite);
    border-bottom: 1px solid #eee;
    padding: 15px 0;
    margin-bottom: 15px;
}

.admin-page-header h2 {
    color: var(--colorBlack);
    font-family: var(--headingFont);
    font-size: 28px;
    font-weight: 600;
    margin: 0;
}

.admin-main-content {
    padding: 20px 30px 50px; /* Reduced top padding for better spacing */
}

@media (max-width: 991.98px) {
    .admin-main-content {
        padding: 30px 15px 50px;
    }

    .admin-header-section {
        padding-top: 80px;
    }
}

/* Admin Navigation Styles */
.admin_menu {
    background: var(--colorWhite);
    box-shadow: var(--boxShadow);
    border-bottom: 2px solid var(--colorPrimary);
}

.admin_menu .nav-link {
    color: var(--colorBlack) !important;
    font-weight: 500;
    padding: 15px 20px;
    transition: all 0.3s ease;
}

.admin_menu .nav-link:hover,
.admin_menu .nav-link.active {
    color: var(--colorPrimary) !important;
    background: rgba(235, 0, 41, 0.1);
    border-radius: 5px;
}

.admin_menu .nav-link i {
    margin-right: 8px;
    font-size: 14px;
}

.admin-user-menu {
    background: var(--gradiantBg) !important;
    color: var(--colorWhite) !important;
    border-radius: 25px !important;
    padding: 8px 20px !important;
    font-weight: 600;
}

.admin-user-dropdown {
    border: none;
    box-shadow: var(--boxShadow);
    border-radius: 10px;
    overflow: hidden;
}

.admin-user-dropdown .dropdown-item {
    padding: 12px 20px;
    color: var(--paraColor);
    transition: all 0.3s ease;
}

.admin-user-dropdown .dropdown-item:hover {
    background: var(--colorPrimary);
    color: var(--colorWhite);
}

.admin-notification {
    position: relative;
    background: #f8f9fa;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex !important;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    transition: all 0.3s ease;
}

.admin-notification:hover {
    background: var(--colorPrimary);
    color: var(--colorWhite) !important;
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--colorPrimary);
    color: var(--colorWhite);
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

/* Admin Dashboard Cards */
.admin-stat-card {
    background: var(--colorWhite);
    border-radius: 15px;
    padding: 30px 25px;
    margin-bottom: 30px;
    box-shadow: var(--boxShadow);
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.admin-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--gradiantBg);
}

.admin-stat-card:hover,
.admin-card-hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.admin-stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    font-size: 24px;
    color: var(--colorWhite);
    background: var(--gradiantBg);
}

.admin-stat-number {
    font-size: 32px;
    font-weight: 700;
    color: var(--colorBlack);
    font-family: var(--headingFont);
    margin: 10px 0 5px;
}

.admin-stat-label {
    color: var(--paraColor);
    font-size: 14px;
    font-weight: 500;
    margin: 0;
}

.admin-stat-change {
    font-size: 12px;
    font-weight: 600;
    margin-top: 8px;
}

.admin-stat-change.positive {
    color: #28a745;
}

.admin-stat-change.negative {
    color: #dc3545;
}

/* Admin Dashboard Tables */
.admin-table-wrapper {
    background: var(--colorWhite);
    border-radius: 15px;
    padding: 25px;
    box-shadow: var(--boxShadow);
    margin-bottom: 30px;
}

.admin-table-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.admin-table-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--colorBlack);
    font-family: var(--headingFont);
    margin: 0;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
}

.admin-table th {
    background: #f8f9fa;
    color: var(--colorBlack);
    font-weight: 600;
    padding: 15px 12px;
    text-align: left;
    border-bottom: 2px solid #dee2e6;
    font-size: 14px;
}

.admin-table td {
    padding: 15px 12px;
    border-bottom: 1px solid #eee;
    color: var(--paraColor);
    font-size: 14px;
}

.admin-table tr:hover {
    background: #f8f9fa;
}

/* Admin Buttons */
.admin-btn {
    background: var(--gradiantBg);
    color: var(--colorWhite) !important;
    border: none;
    padding: 10px 25px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    cursor: pointer;
}

.admin-btn:hover {
    background: var(--gradiantHoverBg);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(235, 0, 41, 0.3);
}

.admin-btn-secondary {
    background: #6c757d;
    color: var(--colorWhite) !important;
}

.admin-btn-secondary:hover {
    background: #5a6268;
}

.admin-btn-success {
    background: #28a745;
    color: var(--colorWhite) !important;
}

.admin-btn-success:hover {
    background: #218838;
}

.admin-btn-danger {
    background: #dc3545;
    color: var(--colorWhite) !important;
}

.admin-btn-danger:hover {
    background: #c82333;
}

.admin-btn-sm {
    padding: 6px 15px;
    font-size: 12px;
}

/* Admin Forms */
.admin-form-group {
    margin-bottom: 20px;
}

.admin-form-label {
    display: block;
    margin-bottom: 8px;
    color: var(--colorBlack);
    font-weight: 600;
    font-size: 14px;
}

.admin-form-control {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: var(--colorWhite);
}

.admin-form-control:focus {
    outline: none;
    border-color: var(--colorPrimary);
    box-shadow: 0 0 0 3px rgba(235, 0, 41, 0.1);
}

/* Admin Breadcrumb */
.admin-breadcrumb {
    background: transparent;
    padding: 0;
    margin-bottom: 20px;
}

.admin-breadcrumb-item {
    color: var(--paraColor);
    font-size: 14px;
}

.admin-breadcrumb-item.active {
    color: var(--colorPrimary);
    font-weight: 600;
}

.admin-breadcrumb-item + .admin-breadcrumb-item::before {
    content: "/";
    margin: 0 8px;
    color: #ccc;
}

/*============================
    COMMON CSS START
============================*/

.common_btn {
    background: var(--gradiantBg);
    text-transform: capitalize !important;
    color: var(--colorWhite) !important;
    padding: 10px 60px 10px 25px !important;
    border-radius: 30px;
    font-size: 15px !important;
    font-weight: 600 !important;
    position: relative;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.common_btn::after {
    position: absolute;
    content: "";
    width: 15px;
    height: 15px;
    border: 2px solid var(--colorWhite);
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
}

.common_btn::before {
    position: absolute;
    content: "";
    width: 15px;
    height: 2px;
    background: var(--colorWhite);
    top: 50%;
    right: 34px;
    transform: translateY(-50%);
    border-radius: 10px;
    transition: all linear .3s;
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -ms-border-radius: 10px;
    -o-border-radius: 10px;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
    -webkit-transform: translateY(-50%);
}

.common_btn:hover {
    background: var(--gradiantHoverBg);
}

.common_btn:hover::before {
    transform: translateY(-50%)scaleX(1);
    -webkit-transform: translateY(-50%)scaleX(1);
    -moz-transform: translateY(-50%)scaleX(1);
    -ms-transform: translateY(-50%)scaleX(1);
    -o-transform: translateY(-50%)scaleX(1);
}

.wsus__section_heading h4 {
    color: var(--colorPrimary);
    font-family: var(--cursiveFont);
    font-size: 22px;
    text-transform: capitalize;
    margin-bottom: 10px;
    text-align: center;
}

.wsus__section_heading h2 {
    text-transform: capitalize;
    font-size: 35px;
    font-weight: 800;
    text-align: center;
}

.wsus__section_heading span {
    display: block;
    max-width: 250px;
    margin: 0 auto;
}

.wsus__section_heading p {
    text-align: center;
    margin-top: 10px;
    padding: 0px 60px;
}

.form-check-input:focus {
    border-color: #eb002975;
    box-shadow: none;
}


/*============================
    COMMON CSS END
============================*/


/*============================
    TOPBAR START
============================*/

.wsus__topbar {
    width: 100%;
    height: 50px;
    background: var(--colorPrimary);
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
}

.wsus__topbar_info {
    line-height: 50px;
}

.wsus__topbar_info li {
    margin-right: 30px;
}

.wsus__topbar_info li:last-child {
    margin: 0;
}

.wsus__topbar_info li a {
    color: var(--colorWhite);
    font-size: 16px;
}

.wsus__topbar_info li a i {
    width: 30px;
    height: 30px;
    line-height: 30px;
    border-radius: 50%;
    text-align: center;
    font-size: 14px;
    margin-right: 10px;
    background: #ca0628;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
}

.wsus__topbar_info li a:hover i {
    background: var(--colorWhite);
    color: var(--colorPrimary);
}

.topbar_icon {
    line-height: 50px;
    justify-content: end;
}

.topbar_icon li a {
    color: var(--colorWhite);
    width: 30px;
    height: 30px;
    line-height: 30px;
    border-radius: 50%;
    background: #ca0628;
    text-align: center;
    margin-left: 10px;
    font-size: 14px;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
    position: relative;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
}

.topbar_icon li a:hover {
    background: var(--colorWhite);
    color: var(--colorPrimary);
}


/*============================
    TOPBAR START
============================*/


/*============================
    MENU START
============================*/

.main_menu {
    position: fixed;
    top: 50px;
    width: 100%;
    height: 80px;
    background: var(--colorWhite);
    border-bottom: 1px solid #eee;
    z-index: 99;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.main_menu .navbar-brand {
    display: inline-block;
    width: 150px;
}

.main_menu .navbar-nav {
    line-height: 80px;
}

.main_menu .navbar-nav .nav-item {
    position: relative;
}

.main_menu .navbar-nav .nav-item .nav-link {
    font-size: 16px;
    font-weight: 600;
    color: var(--colorBlack);
    text-transform: capitalize;
    margin: 0px 15px;
    padding: 0;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
    position: relative;
}

.main_menu .navbar-nav .nav-item .nav-link::after {
    position: absolute;
    content: "";
    width: 40px;
    height: 50px;
    background: url(../images/menu_shapes.png);
    top: 50%;
    left: 50%;
    opacity: 0;
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.main_menu .navbar-nav .nav-item:hover .nav-link,
.main_menu .navbar-nav .nav-item .nav-link.active {
    color: var(--colorPrimary) !important;
}

.main_menu .navbar-nav .nav-item:hover .nav-link::after,
.main_menu .navbar-nav .nav-item .nav-link.active::after {
    opacity: .2;
}

.main_menu .navbar-nav .nav-item .nav-link i {
    font-size: 14px;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.droap_menu {
    position: absolute;
    width: 250px;
    max-height: 500px;
    top: 120%;
    left: 0;
    background: var(--colorWhite);
    box-shadow: var(--boxShadow);
    overflow: hidden;
    overflow-y: auto;
    line-height: 50px;
    opacity: 0;
    visibility: hidden;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    transition: all linear .2s;
    -webkit-transition: all linear .2s;
    -moz-transition: all linear .2s;
    -ms-transition: all linear .2s;
    -o-transition: all linear .2s;
}

.droap_menu::-webkit-scrollbar {
    scrollbar-width: thin !important;
    background: #010f1c;
    width: 6px;
}

.droap_menu::-webkit-scrollbar-thumb {
    background: var(--colorPrimary);
}

.droap_menu li a {
    display: block;
    text-transform: capitalize;
    font-size: 16px;
    font-weight: 500;
    color: var(--colorBlack);
    position: relative;
    padding: 0px 15px 0px 30px;
    border-bottom: 1px solid #eee;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.droap_menu li:last-child a {
    border-bottom: 0;
}

.droap_menu li a::after {
    position: absolute;
    content: "";
    width: 20px;
    height: 1px;
    background: #ddd;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
}

.droap_menu li a::before {
    position: absolute;
    content: "";
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #ddd;
    top: 50%;
    left: 15px;
    transform: translateY(-50%);
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
}

.droap_menu li a:hover,
.droap_menu li a.active {
    color: var(--colorPrimary);
    padding-left: 35px;
}

.droap_menu li a:hover::before,
.droap_menu li a.active::before {
    left: 20px;
    background: var(--colorPrimary);
}

.droap_menu li a:hover::after,
.droap_menu li a.active::after {
    background: var(--colorPrimary);
}

.main_menu .navbar-nav .nav-item:hover .droap_menu {
    top: 100%;
    opacity: 1;
    visibility: visible;
}

.main_menu .menu_icon {
    align-items: center;
}

.main_menu .menu_icon li {
    position: relative;
}

.main_menu .menu_icon li a {
    font-size: 16px;
    font-weight: 600;
    color: var(--colorBlack);
    text-transform: capitalize;
    margin: 0px 10px;
    padding: 0;
    position: relative;
    cursor: pointer;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.main_menu .menu_icon li a:hover,
.main_menu .menu_icon li a.active {
    color: var(--colorPrimary);
}

.main_menu .menu_icon li a span {
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--colorPrimary);
    color: var(--colorWhite);
    line-height: 20px;
    font-size: 12px;
    text-align: center;
    font-weight: 600;
    top: -12px;
    right: -12px;
}

.main_menu .menu_icon li:last-child a {
    padding: 10px 25px !important;
    margin-right: 0;
}

.main_menu .menu_icon li:last-child a::after,
.main_menu .menu_icon li:last-child a::before {
    display: none;
}

.menu_fix {
    top: 0 !important;
    z-index: 9999;
}

.wsus__search_form {
    position: fixed;
    width: 100%;
    height: 100vh;
    top: 0;
    left: 0;
    padding: 10px;
    background: #000000c4;
    border-radius: 5px;
    display: none;
    z-index: 9999;
    justify-content: center;
    align-items: center;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__search_form form {
    width: 40%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
}

.wsus__search_form form .close_search {
    position: absolute;
    width: 50px;
    height: 50px;
    line-height: 50px;
    text-align: center;
    color: var(--colorPrimary);
    background: var(--colorWhite);
    font-size: 25px;
    border-radius: 50%;
    top: -100px;
    right: 0;
    cursor: pointer;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__search_form form .close_search:hover {
    background: var(--gradiantBg);
    color: var(--colorWhite);
}

.wsus__search_form input {
    padding: 15px 30px;
    border-radius: 30px;
}

.wsus__search_form button {
    position: absolute;
    top: 50%;
    right: 7px;
    transform: translateY(-50%);
    background: rgb(156, 3, 30);
    background: var(--gradiantBg);
    text-transform: capitalize;
    color: var(--colorWhite);
    padding: 10px 25px;
    border-radius: 30px;
    font-size: 16px;
    font-weight: 600;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__search_form button:hover {
    background: var(--gradiantHoverBg);
}

.wsus__search_form.show {
    display: block;
}


/* mini cart start */

.wsus__menu_cart_area {
    position: fixed;
    width: 100vw;
    height: 100vh;
    background: #00000066;
    top: 0;
    left: 0;
    z-index: 999;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
    opacity: 0;
    visibility: hidden;
}

.wsus__menu_cart_boody {
    width: 400px;
    position: absolute;
    top: 0;
    right: 0;
    background: var(--colorWhite);
    padding: 20px;
    height: 100vh;
    transform: scaleX(.5);
    transform-origin: right;
    opacity: 0;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__menu_cart_header {
    position: relative;
    margin-bottom: 20px;
}

.wsus__menu_cart_header h5 {
    text-transform: capitalize;
    font-size: 20px;
    font-weight: 600;
}

.wsus__menu_cart_header .close_cart {
    position: absolute;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background: var(--colorPrimary);
    color: var(--colorWhite);
    top: -4px;
    right: 0;
    border-radius: 50%;
    font-size: 16px;
    transition: all linear .3s;
    cursor: pointer;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__menu_cart_header .close_cart:hover {
    background: var(--colorBlack);
    color: var(--colorWhite);
}

.wsus__menu_cart_area ul {
    max-height: 450px;
    overflow-x: hidden;
}

.wsus__menu_cart_area ul::-webkit-scrollbar {
    scrollbar-width: thin !important;
    background: var(--paraColor);
    height: 8px;
    width: 8px;
}

.wsus__menu_cart_area ul::-webkit-scrollbar-thumb {
    background: #d33;
}

.wsus__menu_cart_area ul li {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    position: relative;
    border: 1px solid #eee;
    padding: 10px;
    margin-bottom: 15px;
    background: #f7f5f5;
    border-radius: 5px;
}

.wsus__menu_cart_area ul li:last-child {
    margin-bottom: 0;
}

.wsus__menu_cart_area ul li .menu_cart_img {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    padding: 2px;
    box-shadow: var(--boxShadow);
    overflow: hidden;
}

.wsus__menu_cart_area ul li .menu_cart_text {
    width: 75%;
}

.wsus__menu_cart_area ul li .menu_cart_text .title {
    font-size: 18px;
    font-family: var(--headingFont);
    text-transform: capitalize;
    font-weight: 600;
    color: var(--colorBlack);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__menu_cart_area ul li .menu_cart_text .size {
    text-transform: capitalize;
    color: var(--colorPrimary);
}

.wsus__menu_cart_area ul li .menu_cart_text .extra {
    text-transform: capitalize;
    display: block;
    padding-left: 10px;
    position: relative;
}

.wsus__menu_cart_area ul li .menu_cart_text .extra::after {
    position: absolute;
    content: "";
    width: 5px;
    height: 5px;
    background: var(--paraColor);
    border-radius: 50%;
    top: 10px;
    left: 0;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
}

.wsus__menu_cart_area ul li .menu_cart_text .price {
    color: var(--colorPrimary);
    font-size: 18px;
    font-weight: 600;
    margin-top: 3px;
}

.wsus__menu_cart_area ul li .menu_cart_text .price del {
    color: #747272;
    margin-left: 10px;
    font-size: 15px;
}

.wsus__menu_cart_area ul li .del_icon {
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    cursor: pointer;
    width: 25px;
    height: 25px;
    line-height: 25px;
    border-radius: 50%;
    font-size: 14px;
    background: #ffd7de;
    text-align: center;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
}

.wsus__menu_cart_area ul li .del_icon:hover {
    background: var(--colorPrimary);
    color: var(--colorWhite);
}

.wsus__menu_cart_area ul li .menu_cart_text .title:hover {
    color: var(--colorPrimary);
}

.wsus__menu_cart_area .subtotal {
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-transform: uppercase;
    font-size: 16px;
    font-weight: 800;
    color: var(--colorBlack);
    margin: 20px 0px 20px 0px;
}

.wsus__menu_cart_area .subtotal span {
    font-size: 16px;
    font-weight: 800;
    color: var(--colorBlack);
}

.wsus__menu_cart_area .checkout,
.wsus__menu_cart_area .cart_view {
    display: block;
    text-align: center;
    background: var(--gradiantBg);
    margin-top: 10px;
    text-transform: capitalize;
    font-size: 18px;
    font-weight: 500;
    color: var(--colorWhite);
    padding: 10px 0px;
}

.wsus__menu_cart_area .checkout:hover,
.wsus__menu_cart_area .cart_view :hover {
    background: var(--gradiantHoverBg);
}

.wsus__menu_cart_area.show_mini_cart {
    opacity: 1;
    visibility: visible;
}

.show_mini_cart .wsus__menu_cart_boody {
    opacity: 1;
    transform: scale(1);
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
}


/* mini cart end */


/* reservation modal start */

.wsus__reservation .modal {
    background: #0000008a;
}

.wsus__reservation .modal-content {
    border-radius: 10px;
    border: none;
}

.wsus__reservation .modal-header {
    border: none;
    background: #f6eee7fa;
}

.wsus__reservation .modal-title {
    text-align: center;
    width: 100%;
    text-transform: capitalize;
    font-weight: 600;
}

.wsus__reservation_form input[type=date] {
    text-transform: uppercase;
}

.wsus__reservation_form .reservation_input {
    margin-bottom: 20px;
}

.wsus__reservation_form button {
    background: var(--gradiantBg);
    text-transform: capitalize;
    color: var(--colorWhite);
    padding: 10px 25px;
    border-radius: 30px;
    font-size: 16px;
    font-weight: 600;
    width: 100%;
    text-align: center;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__reservation_form button:hover {
    background: var(--gradiantHoverBg);
}


/* reservation modal end */


/*============================
    MENU END
============================*/


/*============================
    BANNER START
============================*/

.wsus__banner {
    padding-top: 130px;
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-size: cover !important;
    height: 100vh;
    position: relative;
    overflow: hidden;
}

.wsus__banner_overlay {
    background: rgb(255 255 255 / 70%);
    height: 100%;
    position: relative;
}

.wsus__banner_overlay div {
    height: 100%;
}

.banner_shape_1 {
    position: absolute;
    width: 130px;
    height: 110px;
    top: 80px;
    left: 4%;
    animation: rotate linear .10s infinite;
    -webkit-animation: rotate linear 50s infinite;
}

.banner_shape_2 {
    position: absolute;
    width: 70px;
    height: 65px;
    top: 80%;
    left: 5%;
    animation: zoom linear 2.5s infinite alternate;
    -webkit-animation: zoom linear 2.5s infinite alternate;
}

.banner_shape_3 {
    position: absolute;
    width: 200px;
    height: 130px;
    top: 0;
    right: 10%;
    animation: shake linear 7s infinite alternate;
    -webkit-animation: shake linear 7s infinite alternate;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        -o-transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
    }
}

@keyframes zoom {
    from {
        transform: scale(1);
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
    }

    to {
        transform: scale(1.5);
        -webkit-transform: scale(1.5);
        -moz-transform: scale(1.5);
        -ms-transform: scale(1.5);
        -o-transform: scale(1.5);
    }
}

@keyframes shake {
    from {
        transform: rotate(0deg);
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        -o-transform: rotate(0deg);
    }

    to {
        transform: rotate(10deg);
        -webkit-transform: rotate(10deg);
        -moz-transform: rotate(10deg);
        -ms-transform: rotate(10deg);
        -o-transform: rotate(10deg);
    }
}

.wsus__banner_text {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-bottom: 40px;
}

.wsus__banner_text h1 {
    font-size: 58px;
    font-weight: 700;
    text-transform: capitalize;
}

.wsus__banner_text h3 {
    color: var(--colorPrimary) !important;
    text-transform: capitalize;
    font-size: 30px;
    font-weight: 600;
    margin-top: 10px;
}

.wsus__banner_text p {
    font-size: 18px;
    margin: 15px 0px 28px 0px;
}

.wsus__banner_text ul li a {
    margin-right: 20px;
}

.wsus__banner_img {
    display: flex;
    align-items: center;
    position: relative;
}

.wsus__banner_img .img {
    height: 500px;
    width: 500px;
    position: relative;
}

.wsus__banner_img span {
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-size: cover !important;
    position: absolute;
    width: 120px;
    height: 120px;
    top: -20px;
    right: 40px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    color: var(--colorWhite);
    font-size: 22px;
    font-family: var(--cursiveFont);
    letter-spacing: 2px;
    font-weight: 900;
    text-transform: lowercase;
    animation: zoom2 linear 2.5s infinite alternate;
    -webkit-animation: zoom2 linear 2.5s infinite alternate;
}

@keyframes zoom2 {
    from {
        transform: scale(.8);
        -webkit-transform: scale(.8);
        -moz-transform: scale(.8);
        -ms-transform: scale(.8);
        -o-transform: scale(.8);
    }

    to {
        transform: scale(1);
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
    }
}

.wsus__banner_img span img {
    width: 100% !important;
    height: 100% !important;
}

.wsus__blog_det_slider .slick-dots,
.wsus__banner .slick-dots,
.testi_slider .slick-dots,
.wsus__team .slick-dots {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    bottom: 5px;
    width: auto;
    left: 50%;
    transform: translateX(-50%);
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -o-transform: translateX(-50%);
}

.wsus__banner .slick-dots {
    bottom: 15px;
}

.wsus__blog_det_slider .slick-dots li button,
.wsus__banner .slick-dots li button,
.testi_slider .slick-dots li button,
.wsus__team .slick-dots li button {
    font-size: 0;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 1px solid var(--colorPrimary);
    margin: 0px 3px;
    padding: 0;
    opacity: .2;
    transition: all linear .2s;
    -webkit-transition: all linear .2s;
    -moz-transition: all linear .2s;
    -ms-transition: all linear .2s;
    -o-transition: all linear .2s;
    position: relative;
}

.wsus__blog_det_slider .slick-dots li button::after,
.wsus__banner .slick-dots li button::after,
.testi_slider .slick-dots li button::after,
.wsus__team .slick-dots li button::after {
    position: absolute;
    content: "";
    width: 8px;
    height: 8px;
    background: var(--colorPrimary);
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
}

.wsus__blog_det_slider .slick-dots li.slick-active button,
.wsus__banner .slick-dots li.slick-active button,
.testi_slider .slick-dots li.slick-active button,
.wsus__team .slick-dots li.slick-active button {
    opacity: 1;
}


/*============================
    BANNER END
============================*/


/*============================
    WHY CHOOSE START
============================*/

.wsus__why_choose {
    padding-bottom: 25px;
    background: var(--gradiantBg);
    overflow: hidden;
}

.wsus__choose_single {
    margin-top: 25px;
    border-right: 1px dashed #fff;
}

.wsus__choose_single .icon {
    width: 60px;
    height: 60px;
    text-align: center;
    line-height: 60px;
    border-radius: 50%;
    font-size: 30px;
    color: var(--colorPrimary);
}

.wsus__choose_single .icon_1 {
    background: #d1f8ea;
}

.wsus__choose_single .icon_2 {
    background: #f9ebdf;
}

.wsus__choose_single .icon_3 {
    background: #f5d6db;
}

.wsus__choose_single .text {
    max-width: 77%;
}

.wsus__choose_single .text h3 {
    text-transform: capitalize;
    font-size: 23px;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--colorWhite);
}

.wsus__choose_single .text p {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    color: var(--colorWhite);
}


/*============================
    WHY CHOOSE END
============================*/


/*============================
    OFFER ITEM START
============================*/

.wsus__offer_item {
    position: relative;
}

.wsus__offer_item_single {
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-size: cover !important;
    padding: 25px 170px 25px 25px;
    border-radius: 5px;
    border: 1px solid #eee;
    margin: 0px 12px;
}

.wsus__offer_item_single span {
    font-family: var(--cursiveFont);
    letter-spacing: 2px;
    font-size: 20px;
    font-weight: 600;
    color: var(--colorPrimary);
    text-transform: capitalize;
    display: block;
}

.wsus__offer_item_single .title {
    text-transform: capitalize;
    font-size: 20px;
    font-weight: 800;
    margin: 10px 0px 5px;
    transition: all linear .3s;
    display: block;
    color: var(--colorBlack);
    font-family: var(--headingFont);
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__offer_item_single p {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.wsus__offer_item_single ul {
    margin-top: 15px;
}

.wsus__offer_item_single ul li a {
    width: 30px;
    height: 30px;
    background: var(--gradiantBg);
    text-align: center;
    margin-right: 5px;
    line-height: 30px;
    color: var(--colorWhite);
    font-size: 15px;
    border-radius: 3px;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__offer_item_single:hover .title {
    color: var(--colorPrimary);
}

.wsus__offer_item_single ul li a:hover {
    background: var(--gradiantHoverBg);
}

.add_slider .nextArrow,
.add_slider .prevArrow,
.wsus__related_menu .nextArrow,
.wsus__related_menu .prevArrow,
.wsus__brand .nextArrow,
.wsus__brand .prevArrow,
.offer_item_slider .nextArrow,
.offer_item_slider .prevArrow {
    width: 40px;
    height: 40px;
    background: var(--gradiantBg);
    line-height: 40px;
    text-align: center;
    color: var(--colorWhite);
    border-radius: 50%;
    position: absolute;
    top: 50%;
    right: -7px;
    transform: translateY(-50%);
    opacity: .2;
    z-index: 1;
    cursor: pointer;
    transition: all linear .3s;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.add_slider .prevArrow,
.wsus__related_menu .prevArrow,
.wsus__brand .prevArrow,
.offer_item_slider .prevArrow {
    right: auto;
    left: -7px;
}

.add_slider:hover .nextArrow,
.add_slider:hover .prevArrow,
.wsus__related_menu:hover .nextArrow,
.wsus__related_menu:hover .prevArrow,
.wsus__brand:hover .nextArrow,
.wsus__brand:hover .prevArrow,
.offer_item_slider:hover .nextArrow,
.offer_item_slider:hover .prevArrow {
    opacity: 1;
}


/* CART POPUT START */

.wsus__cart_popup .btn-close {
    position: absolute;
    top: 10px;
    right: 10px;
    border: 1px solid var(--colorPrimary);
    overflow: hidden;
    width: 20px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    font-size: 17px;
    background: var(--colorWhite);
    border-radius: 50%;
    opacity: 1;
    color: var(--colorPrimary);
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__cart_popup .btn-close:hover {
    background: var(--colorPrimary);
    color: var(--colorWhite);
}

.wsus__cart_popup_img {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    margin: 0 auto;
}

.wsus__cart_popup_text .title {
    font-size: 27px;
    font-weight: 600;
    text-transform: capitalize;
    margin-top: 20px;
    border-top: 1px solid #eee;
    padding-top: 15px;
    font-family: var(--headingFont);
    color: var(--colorBlack);
    display: block;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__cart_popup_text .title:hover {
    color: var(--colorPrimary);
}

.wsus__cart_popup_text .rating {
    color: #ff9933;
    font-size: 14px;
    text-align: left;
    display: block;
    margin: 3px 0px 20px 0px;
}

.wsus__cart_popup_text .rating span {
    font-size: 14px;
    margin-left: 10px;
}

.wsus__cart_popup_text .price {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.wsus__cart_popup_text .price del {
    font-size: 16px;
    color: var(--colorPrimary);
    font-weight: 500;
    margin-left: 10px;
}


/* CART POPUT END */


/*============================
    OFFER ITEM END
============================*/


/*============================
    MENU ITEM START
============================*/

.wsus__menu {
    position: relative;
}

.wsus__menu .banner_shape_1 {
    position: absolute;
    width: 200px;
    height: 170px;
    top: 0;
}

.wsus__menu .banner_shape_2 {
    width: 200px;
    height: 150px;
    top: 0;
    left: auto;
    right: 5%;
    animation: zoom linear 2.5s infinite alternate;
    -webkit-animation: zoom linear 10s infinite alternate;
}

.wsus__menu .banner_shape_3 {
    width: 200px;
    height: 150px;
    top: 0;
    left: auto;
    right: 5%;
    animation: zoom linear 2.5s infinite alternate;
    -webkit-animation: zoom linear 2.5s infinite alternate;
}

.menu_filter button {
    padding: 5px 25px;
    text-transform: capitalize;
    color: var(--colorPrimary);
    margin: 5px 5px 0px 5px;
    border-radius: 30px;
    background: none;
    border: 1px solid var(--colorPrimary);
    font-weight: 500;
    font-size: 15px;
}

.menu_filter button:hover,
.menu_filter button.active {
    background: var(--gradiantBg);
    color: var(--colorWhite);
}

.wsus__menu_item {
    margin-top: 75px;
    position: relative;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__menu_item .wsus__menu_item_img {
    width: 200px;
    height: 200px;
    position: absolute;
    top: -50px;
    left: 50%;
    border: 5px solid var(--colorWhite);
    transform: translateX(-50%);
    border-radius: 50%;
    box-shadow: var(--boxShadow);
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -o-transform: translateX(-50%);
}

.wsus__menu_item .wsus__menu_item_img .category {
    text-transform: capitalize;
    font-size: 16px;
    color: var(--colorPrimary);
    font-family: var(--cursiveFont);
    position: absolute;
    bottom: 0;
    left: 0;
    background: var(--colorWhite);
    padding: 3px 15px;
    box-shadow: var(--boxShadow);
    border-radius: 30px;
}

.wsus__menu_item .wsus__menu_item_text {
    box-shadow: var(--boxShadow);
    border-radius: 10px;
    background: var(--colorWhite);
    padding: 175px 30px 30px 30px;
    text-align: center;
    border-bottom: 5px solid transparent;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__menu_item .wsus__menu_item_text .rating {
    color: #ff9933;
    font-size: 14px;
    text-align: center;
}

.wsus__menu_item .wsus__menu_item_text .rating span {
    font-size: 14px;
    font-weight: 600;
    padding-left: 20px;
    position: relative;
}

.wsus__menu_item .wsus__menu_item_text .rating span::after {
    position: absolute;
    content: "";
    width: 10px;
    height: 1px;
    background: var(--paraColor);
    top: 10px;
    left: 4px;
}

.wsus__menu_item .wsus__menu_item_text .title {
    text-transform: capitalize;
    color: var(--colorBlack);
    font-size: 24px;
    font-family: var(--headingFont);
    font-weight: 600;
    margin: 10px 0px 12px 0px;
    display: block;
    text-align: center;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__menu_item .wsus__menu_item_text .price {
    font-size: 16px;
    font-weight: 600;
    color: var(--colorPrimary);
    text-transform: capitalize;
    margin-bottom: 20px;
    text-align: center;
}

.wsus__menu_item .wsus__menu_item_text .price del {
    color: var(--paraColor);
    margin-left: 5px;
}

.wsus__menu_item .wsus__menu_item_text ul li a {
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background: var(--gradiantBg);
    color: var(--colorWhite);
    font-size: 14px;
    margin: 0px 5px;
    border-radius: 3px;
}

.wsus__menu_item:hover {
    margin-top: 70px;
}

.wsus__menu_item:hover .wsus__menu_item_text {
    border-color: var(--colorPrimary);
}

.wsus__menu_item:hover .title {
    color: var(--colorPrimary);
}

.wsus__menu_item .wsus__menu_item_text ul li a:hover {
    background: var(--gradiantHoverBg);
}


/*============================
    MENU ITEM END
============================*/


/*============================
    OFFER SLIDER START
============================*/

.wsus__add_slider_single {
    display: block;
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-size: cover !important;
    border-radius: 10px;
    padding: 65px 25px;
    margin: 0px 12px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -ms-border-radius: 10px;
    -o-border-radius: 10px;
}

.wsus__add_slider_single .text {
    width: 60%;
}

.wsus__add_slider_single h3 {
    color: var(--colorWhite);
    font-size: 25px;
    font-weight: 600;
    margin-bottom: 10px;
    text-transform: capitalize;
}

.wsus__add_slider_single p {
    color: var(--colorWhite);
}


/*============================
    OFFER SLIDER END
============================*/


/*============================
    TEAM START
============================*/

.wsus__team {
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-size: cover !important;
    position: relative;
}

.wsus__team .banner_shape_1 {
    width: 200px;
    height: 171px;
    left: auto;
    right: 10%;
}

.wsus__team .banner_shape_2 {
    top: 80px;
    left: 5%;
}

.wsus__single_team {
    box-shadow: var(--boxShadow);
    border-radius: 10px;
    background: var(--colorWhite);
    padding: 175px 30px 30px 30px;
    text-align: center;
    position: relative;
    margin-top: 75px;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__single_team_img {
    width: 190px;
    height: 190px;
    position: absolute;
    top: -50px;
    left: 50%;
    border: 5px solid var(--colorPrimary);
    transform: translateX(-50%);
    border-radius: 50%;
    box-shadow: var(--boxShadow);
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    overflow: hidden;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__single_team_text h4 {
    text-transform: capitalize;
    color: var(--colorBlack);
    font-size: 24px;
    font-family: var(--headingFont);
    font-weight: 600;
    display: block;
    text-align: center;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__single_team_text p {
    text-transform: capitalize;
    margin-bottom: 15px;
    padding-bottom: 10px;
    margin-top: 5px;
    border-bottom: 1px solid #eee;
    text-align: center;
}

.wsus__single_team_text ul li a {
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background: var(--gradiantBg);
    color: var(--colorWhite);
    font-size: 14px;
    margin: 0px 5px;
    border-radius: 50%;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
}

.wsus__single_team_text ul li a:hover {
    background: var(--gradiantHoverBg);
}

.team_slider .wsus__single_team {
    margin: 75px 12px 10px 12px;
}

.wsus__team .slick-dots {
    bottom: -50px;
}

.wsus__single_team:hover {
    transform: scale(1.03);
    -webkit-transform: scale(1.03);
    -moz-transform: scale(1.03);
    -ms-transform: scale(1.03);
    -o-transform: scale(1.03);
}

.wsus__single_team:hover h4 {
    color: var(--colorPrimary);
}

.wsus__single_team:hover ul li a {
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px;
}

.wsus__single_team:hover .wsus__single_team_img {
    border: 5px solid var(--colorWhite);
}


/*============================
    TEAM END
============================*/


/*============================
    DOWNLOAD APP START
============================*/

.wsus__download_bg {
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-size: cover !important;
    border-radius: 10px;
    overflow: hidden;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -ms-border-radius: 10px;
    -o-border-radius: 10px;
}

.wsus__download_overlay {
    background: #faf6f3fa;
    padding: 70px 70px 60px 70px;
}

.wsus__download_text {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-bottom: 15px;
}

.wsus__download_text h4,
.wsus__download_text h2,
.wsus__download_text span,
.wsus__download_text p {
    text-align: left;
}

.wsus__download_text span {
    margin: 0;
}

.wsus__download_text p {
    padding: 0;
    margin: 30px 0px 15px 0px;
}

.wsus__download_text ul li a {
    background: var(--gradiantBg);
    padding: 8px 30px;
    border-radius: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 15px;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__download_text ul li:last-child a {
    margin-right: 0;
}

.wsus__download_text ul li a:hover {
    background: var(--gradiantHoverBg);
}

.wsus__download_text ul li a i {
    font-size: 28px;
    color: var(--colorWhite);
    margin-right: 10px;
}

.wsus__download_text ul li a p {
    text-transform: capitalize;
    font-size: 17px;
    font-weight: 600;
    color: var(--colorWhite);
    margin: 0;
}

.wsus__download_text ul li a p span {
    display: block;
    text-transform: capitalize;
    font-weight: 400;
    color: var(--colorWhite);
    font-size: 12px;
}

.wsus__download_img {
    height: 485px;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
    animation: download_animi linear 5s alternate infinite;
    -webkit-animation: download_animi linear 5s alternate infinite;
}

@keyframes download_animi {
    from {
        transform: rotate(5deg);
        -webkit-transform: rotate(5deg);
        -moz-transform: rotate(5deg);
        -ms-transform: rotate(5deg);
        -o-transform: rotate(5deg);
    }

    to {
        transform: rotate(-5deg);
        -webkit-transform: rotate(-5deg);
        -moz-transform: rotate(-5deg);
        -ms-transform: rotate(-5deg);
        -o-transform: rotate(-5deg);
    }
}


/*============================
    DOWNLOAD APP END
============================*/


/*============================
    COUNTER START
============================*/

.wsus__counter {
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-size: cover !important;
}

.wsus__counter_overlay {
    background: rgb(0 0 0 / 77%);
}

.wsus__single_counter {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    position: relative;
}

.wsus__single_counter i {
    font-size: 50px;
    color: var(--colorPrimary);
    margin-right: 20px;
}

.wsus__single_counter .text h2 {
    color: var(--colorWhite);
    font-size: 40px;
    font-weight: 600;
    position: relative;
    display: inline-block;
}

.wsus__single_counter .text h2::after {
    position: absolute;
    content: "+";
    color: var(--colorWhite);
    font-size: 40px;
    top: -3px;
    right: -30px;
}

.wsus__single_counter .text p {
    color: var(--colorWhite);
    text-transform: capitalize;
    font-size: 18px;
    font-weight: 500;
}


/*============================
    COUNTER END
============================*/


/*============================
    TESTIMONIAL START
============================*/

.wsus__single_testimonial {
    background: var(--colorWhite);
    box-shadow: var(--boxShadow);
    padding: 25px;
    border-radius: 10px;
    position: relative;
    overflow: hidden;
    margin: 5px 12px 0 12px;
}

.wsus__single_testimonial::after {
    position: absolute;
    content: "";
    background: url(../images/quot.png);
    width: 50px;
    height: 50px;
    bottom: 10px;
    right: 15px;
    z-index: 1;
    opacity: .5;
}

.wsus__testimonial_header {
    margin-bottom: 20px;
}

.wsus__testimonial_header .img {
    width: 70px;
    height: 70px;
    margin-right: 15px;
    border-radius: 50%;
    overflow: hidden;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    box-shadow: var(--boxShadow);
    border: 5px solid var(--colorWhite);
}

.wsus__testimonial_header .text h4 {
    text-transform: capitalize;
    font-size: 20px;
    font-weight: 600;
}

.wsus__testimonial_header .text p {
    text-transform: uppercase;
}

.wsus__single_testimonial_body .feedback {
    font-size: 16px;
}

.wsus__single_testimonial_body .rating {
    color: #ff9933;
    font-size: 14px;
    text-align: left;
    display: block;
    margin-top: 20px;
}

.wsus__testimonial_product {
    position: absolute;
    width: 130px;
    height: 130px;
    border-radius: 50%;
    top: -20px;
    right: -20px;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    box-shadow: var(--boxShadow);
    z-index: 1;
}

.testi_slider .slick-dots {
    bottom: -50px;
}


/*============================
    TESTIMONIAL END
============================*/


/*============================
    BLOG START
============================*/

.wsus__blog {
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-size: cover !important;
}

.wsus__blog_overlay {
    background: #faf6f3fa;
}

.wsus__single_blog {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--boxShadow);
    background: var(--colorWhite);
    position: relative;
    margin-top: 25px;
}

.wsus__single_blog_img {
    height: 500px;
    display: block;
}

.wsus__single_blog_img img {
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__single_blog_text {
    background: var(--colorWhite);
    width: 100%;
    padding: 30px;
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 1;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__single_blog_text .category {
    background: var(--gradiantBg);
    color: var(--colorWhite);
    text-transform: capitalize;
    padding: 5px 20px;
    font-weight: 500;
    font-size: 14px;
    border-radius: 30px;
    position: absolute;
    top: -15px;
    left: 30px;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__single_blog_text ul li {
    text-transform: capitalize;
    font-size: 13px;
    font-weight: 400;
    margin-right: 15px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__single_blog_text ul li i {
    width: 20px;
    height: 20px;
    line-height: 20px;
    font-size: 10px;
    text-align: center;
    border-radius: 50%;
    background: var(--gradiantBg);
    color: var(--colorWhite);
    margin-right: 5px;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__single_blog_text .title {
    font-size: 23px;
    font-weight: 700;
    text-transform: capitalize;
    color: var(--colorBlack);
    margin-top: 15px;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.wsus__single_blog:hover .wsus__single_blog_text {
    background: rgb(0, 0, 0);
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.8270658605238971) 0%, rgba(0, 0, 0, 0.527345972568715) 67%, rgba(0, 0, 0, 0) 100%);
}

.wsus__single_blog:hover ul li,
.wsus__single_blog:hover .title {
    color: var(--colorWhite);
}

.wsus__single_blog:hover .wsus__single_blog_img img {
    transform: scale(1.05);
    -webkit-transform: scale(1.05);
    -moz-transform: scale(1.05);
    -ms-transform: scale(1.05);
    -o-transform: scale(1.05);
}

.wsus__single_blog .title:hover {
    color: var(--colorPrimary);
}


/*============================
    BLOG END
============================*/


/*============================
    BRAND START
============================*/

.wsus__brand {
    position: relative;
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-size: cover !important;
}

.wsus__brand_overlay {
    background: rgb(0 0 0 / 81%);
    padding: 35px 0px;
}

.wsus__single_brand {
    position: relative;
    margin: 5px 12px 0px 12px;
    border-radius: 5px;
    height: 105px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.wsus__single_brand img {
    width: auto !important;
    height: auto !important;
}


/*============================
    BRAND END
============================*/


/*============================
    FOOTER START
============================*/

footer {
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-size: cover !important;
}

.footer_overlay {
    background: #b90424fa;
}

.wsus__footer_content .footer_logo {
    width: 150px;
}

.wsus__footer_content span {
    display: block;
    margin: 10px 0px;
    color: var(--colorWhite);
}

.wsus__footer_content .info {
    color: var(--colorWhite);
    padding-left: 35px;
    position: relative;
    margin-top: 15px;
    display: block;
}

.wsus__footer_content .info i {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 25px;
    height: 25px;
    background: var(--gradiantBg);
    border-radius: 50%;
    text-align: center;
    line-height: 25px;
    font-size: 12px;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
}

.wsus__footer_content h3 {
    color: var(--colorWhite);
    font-size: 22px;
    font-weight: 700;
    text-transform: capitalize;
    margin-bottom: 12px;
    border-bottom: 2px solid var(--colorWhite);
    display: inline-block;
    padding-bottom: 5px;
}

.wsus__footer_content ul li a {
    color: var(--colorWhite);
    text-transform: capitalize;
    font-size: 16px;
    margin-top: 15px;
    display: block;
    position: relative;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__footer_content ul li a::after {
    position: absolute;
    content: "";
    width: 8px;
    height: 8px;
    background: var(--colorWhite);
    top: 9px;
    left: 0;
    opacity: 0;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__footer_content ul li a:hover {
    padding-left: 15px;
}

.wsus__footer_content ul li a:hover:after {
    opacity: 1;
}

.wsus__footer_content form {
    position: relative;
    margin-top: 22px;
}

.wsus__footer_content input {
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
}

.wsus__footer_content button {
    background: var(--gradiantBg);
    color: var(--colorWhite);
    text-transform: capitalize;
    padding: 10px 20px;
    border-radius: 5px;
    position: absolute;
    top: 3px;
    right: 3px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__footer_content button:hover {
    background: var(--gradiantHoverBg);
}

.wsus__footer_social_link h5 {
    text-transform: capitalize;
    font-weight: 600;
    font-size: 20px;
    color: var(--colorWhite);
    border-bottom: 2px solid var(--colorWhite);
    display: inline-block;
    margin-top: 40px;
    padding-bottom: 10px;
    margin-bottom: 13px;
}

.wsus__footer_social_link ul li a {
    width: 30px;
    height: 30px;
    line-height: 30px;
    font-size: 14px;
    text-align: center;
    background: #eeeeee5e;
    border-radius: 50%;
    margin: 0px 10px 0px 0px;
    padding: 0 !important;
}

.wsus__footer_social_link ul li a::after,
.wsus__footer_social_link ul li a::before {
    display: none;
}

.wsus__footer_social_link ul li a:hover {
    background: var(--gradiantHoverBg);
}

.wsus__footer_bottom {
    background: var(--gradiantBg);
    padding: 30px 0px;
    border-top: 1px solid #f22020;
}

.wsus__footer_bottom_text p {
    color: var(--colorWhite);
}

.wsus__footer_bottom_text p b {
    text-transform: capitalize;
    color: var(--colorWhite);
    margin: 0px 5px;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
    font-weight: 600;
}

.wsus__footer_bottom_text ul li a {
    color: var(--colorWhite);
    text-transform: capitalize;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
    margin-left: 12px;
    position: relative;
    padding-left: 20px;
}

.wsus__footer_bottom_text ul li a::after {
    position: absolute;
    content: "";
    width: 7px;
    height: 7px;
    background: var(--colorWhite);
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
}

.wsus__footer_bottom_text ul li:first-child a::after {
    display: none;
}

.wsus__footer_bottom_text ul li:first-child a {
    padding-left: 0;
    margin-left: 0;
}

.wsus__footer_bottom_text ul li a:hover {
    color: var(--colorBlack);
}


/* SCROLL BUTTON START */

.wsus__scroll_btn {
    position: fixed;
    bottom: 20px;
    right: 10px;
    z-index: 111;
    cursor: pointer;
    text-align: center;
    transform: rotate(90deg);
    background: var(--gradiantBg);
    color: var(--colorWhite);
    text-transform: capitalize;
    padding: 5px 15px 7px 15px;
    border-radius: 30px;
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    animation: scroll_amini linear 2s infinite alternate;
    -webkit-animation: scroll_amini linear 2s infinite alternate;
}

.wsus__scroll_btn:hover {
    background: var(--gradiantHoverBg);
}

@keyframes scroll_amini {
    from {
        bottom: 100px;
    }

    to {
        bottom: 120px;
    }
}


/* SCROLL BUTTON END */


/*============================
    FOOTER END
============================*/


/*============================
    BREADCRUMB START
============================*/

.wsus__breadcrumb {
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-size: cover !important;
}

.wsus__breadcrumb_overlay {
    padding: 250px 0px 132px 0px;
    background: #000000b3;
    position: relative;
}

.wsus__breadcrumb_text h1 {
    text-align: center;
    color: var(--colorWhite);
    text-transform: capitalize;
    font-size: 50px;
    font-weight: 800;
    margin-bottom: 7px;
}

.wsus__breadcrumb_text ul {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    position: absolute;
    min-width: 265px;
    bottom: -22px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gradiantBg);
    padding: 10px 25px;
    border-radius: 30px;
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    z-index: 1;
}

.wsus__breadcrumb_text ul li a {
    text-transform: capitalize;
    color: var(--colorWhite);
    font-size: 16px;
    position: relative;
    padding-right: 25px;
}

.wsus__breadcrumb_text ul li a::after {
    position: absolute;
    content: "";
    width: 8px;
    height: 1px;
    background: var(--colorWhite);
    top: 13px;
    right: 10px;
}

.wsus__breadcrumb_text ul li:last-child a {
    padding-right: 0;
}

.wsus__breadcrumb_text ul li:last-child a::after {
    display: none;
}

.wsus__breadcrumb_text ul li:first-child a {
    padding-left: 25px;
}

.wsus__breadcrumb_text ul li:first-child a::before {
    position: absolute;
    content: "\f015";
    color: var(--colorWhite);
    font-family: "Font Awesome 5 Free";
    font-weight: 600;
    top: 1px;
    left: 0;
    font-size: 16px;
    border-radius: 50%;
}


/*============================
    BREADCRUMB END
============================*/


/*============================
    PAGINATION START
============================*/

.wsus__pagination ul {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}

.wsus__pagination ul li a {
    width: 50px !important;
    height: 50px !important;
    padding: 0 !important;
    line-height: 50px !important;
    text-align: center !important;
    font-size: 16px !important;
    background: var(--colorWhite);
    color: var(--paraColor) !important;
    border-radius: 0 !important;
    -webkit-border-radius: 0 !important;
    -moz-border-radius: 0 !important;
    -ms-border-radius: 0 !important;
    -o-border-radius: 0 !important;
    margin: 0px 10px;
}

.wsus__pagination ul li a:hover,
.wsus__pagination ul li.active a {
    background: var(--colorPrimary);
    color: var(--colorWhite) !important;
    border-color: var(--colorPrimary);
}

.wsus__pagination ul li a:focus {
    box-shadow: none;
}


/*============================
    PAGINATION END
============================*/


/*============================
    ABOUT PAGE START
============================*/

.wsus__about_us_img {
    border-radius: 10px;
    overflow: hidden;
    height: 460px;
    margin-right: 50px;
}

.play_btn {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 70px;
    height: 70px;
    line-height: 70px;
    font-size: 20px;
    background: var(--colorWhite);
    color: var(--colorPrimary) !important;
    text-align: center;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    z-index: 1;
}

.play_btn::before {
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    background: #fff;
    border-radius: 50%;
    top: 0;
    left: 0;
    z-index: -3;
    animation: play_animate 1.3s infinite;
    -webkit-animation: play_animate 1.3s infinite;
}

@keyframes play_animate {
    from {
        transform: scale(1);
        opacity: 1;
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
    }

    to {
        transform: scale(1.5);
        opacity: 0;
        -webkit-transform: scale(1.5);
        -moz-transform: scale(1.5);
        -ms-transform: scale(1.5);
        -o-transform: scale(1.5);
    }
}

.wsus__about_choose .wsus__section_heading h4,
.wsus__about_choose .wsus__section_heading h2,
.wsus__about_us .wsus__section_heading h4,
.wsus__about_us .wsus__section_heading h2 {
    text-align: left;
}

.wsus__about_choose .wsus__section_heading span,
.wsus__about_us .wsus__section_heading span {
    margin: 0;
}

.wsus__about_choose .wsus__section_heading p,
.wsus__about_us .wsus__section_heading p {
    padding: 0 !important;
    text-align: left;
}

.wsus__about_us_text p {
    margin: 15px 0px;
}

.wsus__about_us_text ul,
.wsus__about_us_text ol {
    display: flex;
    flex-wrap: wrap;
}

.wsus__about_us_text ul li,
.wsus__about_us_text ol li {
    text-transform: capitalize;
    font-size: 16px;
    color: var(--colorBlack);
    width: 50%;
    padding-left: 30px;
    position: relative;
    margin-top: 15px;
}

.wsus__about_us_text ul li::after,
.wsus__about_us_text ol li::after {
    position: absolute;
    content: "\f00c";
    color: var(--colorWhite);
    font-family: "Font Awesome 5 Free";
    font-weight: 600;
    top: 2px;
    left: 0;
    font-size: 10px;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    background: var(--colorPrimary);
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
}

.wsus__about_text-box {
    position: relative;
}

.wsus__about_text-box h4 {
    text-transform: capitalize;
    font-size: 20px;
    font-weight: 600;
}

.wsus__about_choose_bg_area {
    background: #faf6f3fa;
    border: 1px solid #eeeeee85;
    border-radius: 10px;
    overflow: hidden;
}

.wsus__about_choose_img {
    height: 100%;
    position: relative;
}

.wsus__about_choose_img .img_1 {
    width: 350px;
    height: 477px;
    display: block;
    border-radius: 10px;
    overflow: hidden;
}

.wsus__about_choose_img .img_2 {
    position: absolute;
    width: 300px;
    height: 200px;
    top: 30px;
    right: 50px;
    border-radius: 10px;
    overflow: hidden;
}

.wsus__about_choose_img .img_3 {
    position: absolute;
    width: 300px;
    height: 200px;
    bottom: 30px;
    right: 0;
    border-radius: 10px;
    overflow: hidden;
}

.wsus__about_choose_text {
    padding: 25px 25px 25px 0px;
}

.wsus__about_choose_text ul,
.wsus__about_choose_text ol {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.wsus__about_choose_text ul li,
.wsus__about_choose_text ol li {
    width: 49%;
    text-align: center;
    margin-top: 12px;
    padding: 22px;
    position: relative;
    border-radius: 5px;
    background: var(--colorWhite);
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 600;
    color: var(--colorBlack);
}

.wsus__about_choose_text ul li span,
.wsus__about_choose_text ol li span {
    color: var(--colorPrimary);
    font-size: 25px;
    margin-right: 15px;
    width: 60px;
    height: 60px;
    line-height: 60px;
    border-radius: 50%;
    text-align: center;
    background: #faf6f3fa;
}

.wsus__about_video_bg {
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    border-radius: 10px;
    overflow: hidden;
}

.wsus__about_video_overlay {
    position: relative;
    background: #0000009c;
    padding: 150px 0px;
}

.wsus__about_video_text {
    text-align: center;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
}

.wsus__about_video_text p {
    font-size: 35px;
    font-family: var(--cursiveFont);
    text-transform: capitalize;
    color: var(--colorWhite);
    text-align: center;
    margin-right: 50px;
}

.wsus__about_video_text .play_btn {
    position: inherit;
    transform: translate(0px, 0px);
}


/*============================
    ABOUT PAGE END
============================*/


/*============================
    MENU PAGE START
============================*/

.wsus__menu_page .menu_heading {
    color: var(--colorPrimary);
    text-transform: capitalize;
    font-size: 35px;
    font-family: var(--cursiveFont);
    border-bottom: 1px dashed #eb00292b;
}

.wsus__menu_area {
    border: 1px dashed #eb00292b;
}


/*============================
    MENU PAGE END
============================*/


/*============================
    CONTACT START
============================*/

.wsus__contact_info {
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-size: cover !important;
    padding: 40px;
    box-shadow: var(--boxShadow);
    border-radius: 10px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -ms-border-radius: 10px;
    -o-border-radius: 10px;
    margin-top: 25px;
}

.wsus__contact_info span {
    width: 70px;
    height: 70px;
    display: block;
    line-height: 70px;
    text-align: center;
    background: var(--colorPrimary);
    border-radius: 50%;
    color: var(--colorWhite);
    font-size: 30px;
    margin: 0 auto;
    position: relative;
}

.wsus__contact_info span::after {
    position: absolute;
    content: "";
    width: 80px;
    height: 80px;
    top: -5px;
    left: -5px;
    border: 1px dashed var(--colorPrimary);
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    animation: roatate linear 30s infinite;
    -webkit-animation: roatate linear 30s infinite;
}

@keyframes roatate {
    from {
        transform: rotate(0deg);
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        -o-transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
    }
}

.wsus__contact_info h3 {
    text-transform: capitalize;
    font-size: 30px;
    font-weight: 600;
    text-align: center;
    margin: 22px 0px 15px 0px;
}

.wsus__contact_info p {
    display: block;
    text-align: center;
    font-size: 16px;
    color: var(--colorBlack);
    line-height: 29px;
}

.wsus__contact_form {
    background: #faf6f3fa;
    padding: 30px;
    border-radius: 10px;
}

.wsus__contact_form h3 {
    text-transform: capitalize;
    font-size: 30px;
    font-weight: 600;
}

.wsus__contact_form_input {
    position: relative;
    margin-top: 20px;
}

.wsus__contact_form_input span {
    position: absolute;
    display: block;
    width: 50px;
    height: 100%;
    line-height: 50px;
    text-align: center;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    color: var(--colorPrimary);
    border-right: 1px solid #E4E7E9;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
}

.wsus__contact_form_input input,
.wsus__contact_form_input textarea {
    padding: 12px 20px 12px 65px;
    border-radius: 5px;
}

.wsus__contact_form_input.textarea span {
    height: 96%;
    top: 0;
    transform: translate(0);
}

.wsus__contact_form button {
    background: var(--gradiantBg);
    text-transform: capitalize !important;
    color: var(--colorWhite) !important;
    padding: 10px 20px;
    border-radius: 30px;
    font-size: 16px !important;
    font-weight: 600 !important;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
    margin-top: 20px;
}

.wsus__contact_form button:hover {
    background: var(--gradiantHoverBg);
}

.wsus__contact_map {
    width: 100%;
    height: 100%;
    border-radius: 10px;
    overflow: hidden;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -ms-border-radius: 10px;
    -o-border-radius: 10px;
}

.wsus__contact_map iframe {
    width: 100%;
    height: 100%;
}


/*============================
    CONTACT END
============================*/


/*================================
    BLOG DETAILS START
================================*/

.wsus__blog_details_img {
    height: 450px;
}

.wsus__blog_det_area {
    background: #FFFFFF;
    box-shadow: var(--boxShadow);
    border-radius: 5px;
    overflow: hidden;
}

.wsus__blog_details_text {
    padding: 30px;
}

.wsus__blog_details_text .details_bloger {
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
    margin-bottom: 20px;
}

.wsus__blog_details_text .details_bloger li {
    font-size: 15px;
    font-weight: 400;
    color: var(--paraColor);
    margin-right: 50px;
}

.wsus__blog_details_text .details_bloger li i {
    color: var(--colorPrimary);
    width: 20px;
}

.wsus__blog_det_slider {
    margin-bottom: 30px;
}

.wsus__blog_details_text h1,
.wsus__blog_details_text h2,
.wsus__blog_details_text h3,
.wsus__blog_details_text h4,
.wsus__blog_details_text h5,
.wsus__blog_details_text h6 {
    font-weight: 800;
    font-family: var(--headingFont);
    margin-bottom: 25px;
}

.wsus__blog_details_text h1,
.wsus__blog_details_text h2 {
    font-size: 33px;
}

.wsus__blog_details_text h3 {
    font-size: 30px;
}

.wsus__blog_details_text h4 {
    font-size: 25px;
}

.wsus__blog_details_text h5 {
    font-size: 20px;
}

.wsus__blog_details_text p {
    margin-bottom: 25px;
}

.wsus__blog_quot_text {
    background: #faf6f3fa;
    border-radius: 5px;
    padding: 35px 35px 35px 70px;
    border-left: 5px solid var(--colorPrimary);
    position: relative;
    margin-bottom: 30px;
}

.wsus__blog_quot_text p {
    font-weight: 500;
}

.wsus__blog_quot_text h4 {
    text-transform: capitalize;
    font-size: 20px;
    font-weight: 700;
    color: var(--colorPrimary);
    margin: 0;
}

.wsus__blog_quot_text h4 span {
    margin-left: 10px;
    display: inline-block;
    font-size: 14px;
}

.wsus__blog_quot_text .left_icon {
    color: var(--colorPrimary);
    font-size: 20px;
    display: block;
    position: absolute;
    left: 23px;
    top: 25px;
    transform: rotateY(180deg);
    width: 35px;
    -webkit-transform: rotateY(180deg);
    -moz-transform: rotateY(180deg);
    -ms-transform: rotateY(180deg);
    -o-transform: rotateY(180deg);
}

.wsus__blog_quot_text p span {
    position: absolute;
    margin-left: 20px;
    width: 35px;
}

.wsus__blog_det_slider_item {
    height: 250px;
    border-radius: 5px;
    margin: 0px 12px;
    overflow: hidden;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
}

.blog_details_list li {
    position: relative;
    padding-left: 40px;
    margin-top: 15px;
}

.blog_details_list li::after {
    position: absolute;
    content: "\f00c";
    color: var(--colorWhite);
    font-family: "Font Awesome 5 Free";
    font-weight: 600;
    top: 0px;
    left: 0;
    width: 25px;
    height: 25px;
    line-height: 25px;
    text-align: center;
    font-size: 12px;
    border-radius: 50%;
    background: var(--colorPrimary);
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
}

.blog_tags_share {
    border-top: 1px solid #E9F2FF;
    margin-top: 30px;
    padding-top: 20px;
}

.blog_tags_share span {
    font-size: 20px;
    font-weight: 700;
    color: var(--colorBlack);
    text-transform: capitalize;
    margin-right: 10px;
}

.blog_tags_share .tags ul li a,
.blog_tags_share .share ul li a {
    font-size: 16px;
    font-weight: 400;
    color: var(--colorBlack);
    text-transform: capitalize;
    margin-right: 10px;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.blog_tags_share .tags ul li a:hover {
    color: var(--colorPrimary);
}

.blog_tags_share .share ul li a {
    width: 30px;
    height: 30px;
    text-align: center;
    line-height: 32px;
    background: var(--gradiantBg);
    color: var(--colorWhite);
    border-radius: 50%;
    font-size: 14px;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.blog_tags_share .share ul li a:hover {
    background: var(--gradiantHoverBg);
}

.blog_tags_share .tags ul li:last-child a,
.blog_tags_share .share ul li:last-child a {
    margin-right: 0;
}

.blog_det_button {
    display: flex;
    justify-content: space-between;
    align-content: center;
    flex-wrap: wrap;
}

.blog_det_button li {
    width: 48%;
    box-shadow: var(--boxShadow);
    border-radius: 5px;
    background: var(--colorWhite);
    padding: 25px;
}

.blog_det_button li a {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.blog_det_button li a img {
    width: 110px !important;
    height: 150px !important;
    border-radius: 5px;
}

.blog_det_button li a p {
    font-size: 18px;
    font-weight: 700;
    color: var(--colorBlack);
    width: 60%;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
    font-family: var(--headingFont);
}

.blog_det_button li a p span {
    display: block;
    font-size: 18px;
    text-transform: capitalize;
    color: var(--colorPrimary);
    margin-top: 20px;
}

.blog_det_button li:first-child a p {
    text-align: right;
}

.blog_det_button li:first-child a span i {
    margin-right: 10px;
}

.blog_det_button li:last-child a p {
    text-align: left;
}

.blog_det_button li:last-child a span i {
    margin-left: 10px;
}

.blog_det_button li a:hover p {
    color: var(--colorPrimary);
}

.wsus__comment {
    overflow: hidden;
    background: var(--colorWhite);
    box-shadow: var(--boxShadow);
    border-radius: 5px;
    padding: 25px;
}

.wsus__comment h4 {
    font-size: 24px;
    font-weight: 700;
    color: var(--colorBlack);
    font-family: var(--headingFont);
}

.wsus__single_comment {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    border-top: 1px solid #eee;
    margin-top: 25px;
    padding-top: 25px;
}

.wsus__single_comment img {
    width: 100px !important;
    height: 100px !important;
    border-radius: 50%;
    box-shadow: var(--boxShadow);
    border: 5px solid var(--colorWhite);
}

.wsus__single_comm_text {
    width: 85%;
}

.wsus__single_comm_text h3 {
    font-size: 20px !important;
    font-weight: 600 !important;
    color: var(--colorBlack) !important;
    text-transform: capitalize !important;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    margin: 0 !important;
}

.wsus__single_comm_text h3 span {
    color: var(--colorPrimary);
    text-transform: capitalize;
    font-size: 14px;
}

.wsus__single_comm_text h3 a {
    color: var(--colorBlack);
    font-size: 20px;
    font-weight: 600;
    text-transform: capitalize;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__single_comm_text a:hover {
    color: var(--colorPrimary);
}

.wsus__single_comm_text .rating {
    color: #ff9933;
    font-size: 14px;
    display: block;
    margin-top: 8px;
}

.wsus__single_comm_text .rating b {
    font-size: 14px;
    margin-left: 10px;
    font-weight: 400;
    color: var(--paraColor);
}

.wsus__single_comm_text p {
    margin: 8px 0px;
}

.wsus__single_comm_text a {
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__single_comment .status {
    background: #009d00;
    color: var(--colorWhite);
    padding: 2px 15px 4px 15px;
    text-transform: capitalize;
    font-size: 16px;
    font-weight: 400;
    border-radius: 30px;
    margin-top: 5px;
    display: inline-block;
}

.wsus__single_comment .active {
    background: rgb(0 236 173);
}

.wsus__single_comment .inactive {
    background: #f5506d;
}

.comment_input {
    overflow: hidden;
    background: var(--colorWhite);
    box-shadow: var(--boxShadow);
    border-radius: 5px;
    padding: 25px;
}

.comment_input h4 {
    font-size: 24px;
    font-weight: 700;
    color: var(--colorBlack);
    text-transform: capitalize;
}

.comment_input p {
    font-size: 14px;
    font-weight: 400;
    color: var(--colorBlack);
    margin-top: 14px;
}

.blog_sidebar {
    background: var(--colorWhite);
    box-shadow: var(--boxShadow);
    border-radius: 5px;
    padding: 25px;
    margin-top: 25px;
}

.blog_sidebar h3 {
    font-size: 22px;
    font-weight: 700;
    text-transform: capitalize;
    border-left: 3px solid var(--colorPrimary);
    margin-bottom: 15px;
    padding-left: 10px;
    color: var(--colorPrimary);
    line-height: 24px;
}

.blog_sidebar form {
    position: relative;
}

.blog_sidebar form input {
    background: #F5F9FF;
    border-radius: 5px;
}

.blog_sidebar form button {
    position: absolute;
    top: 10px;
    right: 20px;
    font-size: 18px;
    color: var(--colorPrimary);
    background: none;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.blog_sidebar form button:hover {
    color: var(--colorBlack);
}

.wsus__related_blog ul li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
}

.wsus__related_blog ul li img {
    width: 80px !important;
    height: 80px !important;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
}

.wsus__related_blog ul li .text {
    width: 72%;
}

.wsus__related_blog ul li .text a {
    font-size: 16px;
    font-weight: 600;
    color: var(--colorBlack);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    margin-bottom: 7px;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__related_blog ul li .text a:hover {
    color: var(--colorPrimary);
}

.wsus__related_blog ul li .text p {
    font-size: 14px;
    text-transform: capitalize;
}

.wsus__related_blog ul li .text p i {
    width: 20px;
    color: var(--colorPrimary);
}

.wsus__blog_categori ul li a {
    text-transform: capitalize;
    font-size: 16px;
    font-weight: 400;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #F5F9FF;
    color: var(--colorBlack);
    padding-left: 20px;
    margin-top: 10px;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__blog_categori ul li a span {
    background: var(--gradiantBg);
    width: 35px;
    height: 35px;
    line-height: 35px;
    text-align: center;
    color: var(--colorWhite);
    border-left: 1px solid transparent;
}

.wsus__blog_categori ul li a:hover span {
    border-color: var(--colorWhite);
}

.wsus__blog_categori ul li a:hover {
    background: var(--gradiantBg);
    color: var(--colorWhite);
}

.wsus__blog_tags ul {
    display: flex;
    flex-wrap: wrap;
}

.wsus__blog_tags ul li a {
    background: #F5F9FF;
    border-radius: 3px;
    font-size: 16px;
    font-weight: 400;
    color: var(--paraColor);
    padding: 7px 17px;
    margin-top: 10px;
    margin-right: 10px;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__blog_tags ul li a:hover {
    background: var(--colorPrimary);
    color: var(--colorWhite);
}

.wsus__blog_page .wsus__single_blog {
    margin-top: 0;
    margin-bottom: 25px;
}

.wsus__blog_det_slider .slick-dots {
    bottom: -30px;
}

.wsus__blog_details_text h3 {
    font-size: 25px;
    font-weight: 600;
    margin-bottom: 25px;
    margin-top: 55px;
}

.comment_input label {
    display: block;
    text-transform: capitalize;
    font-size: 15px;
    font-weight: 500;
    color: var(--colorBlack);
    margin-top: 20px;
}

.comment_input .wsus__contact_form_input {
    margin-top: 5px;
}

.wsus_dash_personal_info button {
    margin-top: 15px;
}


/*================================
    BLOG DETAILS END
================================*/


/*============================
    MENU DETAILS START
============================*/

.wsus__menu_details_images {
    border: 1px solid #eee;
    border-radius: 10px;
    background: #faf6f3fa;
}

.wsus__menu_details_text h2 {
    font-size: 40px;
    font-weight: 700;
}

.wsus__menu_details_text .rating {
    color: #ff9933;
    font-size: 14px;
    text-align: left;
    display: block;
    margin: 17px 0px 25px 0px;
}

.wsus__menu_details_text .rating span {
    font-size: 14px;
    margin-left: 10px;
}

.wsus__menu_details_text .price {
    font-size: 25px;
    font-weight: 700;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.wsus__menu_details_text .price del {
    font-size: 18px;
    color: var(--colorPrimary);
    font-weight: 500;
    margin-left: 10px;
}

.wsus__menu_details_text .short_description {
    margin-bottom: 25px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.details_size,
.details_extra_item,
.details_quentity {
    margin-bottom: 30px;
}

.details_size h5,
.details_extra_item h5,
.details_quentity h5 {
    text-transform: capitalize;
    font-size: 18px;
    font-weight: 600;
    color: var(--colorBlack);
    margin-bottom: 15px;
}

.details_size .form-check input {
    padding: 0;
}

.details_extra_item h5 span {
    text-transform: capitalize;
}

.details_extra_item .form-check {
    margin-bottom: 10px;
}

.details_extra_item .form-check input {
    padding: 0;
    border-radius: 2px;
}

.details_size .form-check input:checked,
.details_extra_item .form-check input:checked {
    background-color: var(--colorPrimary);
    border-color: var(--colorPrimary);
}

.details_size .form-check label,
.details_extra_item .form-check label {
    text-transform: capitalize;
    color: var(--colorBlack);
    font-size: 16px;
    font-weight: 400;
    display: inline-flex;
    flex-wrap: wrap;
    justify-content: space-between;
    min-width: 35%;
}

.quentity_btn {
    display: flex;
    align-items: center;
    justify-content: start;
}

.quentity_btn button {
    width: 35px;
    height: 35px;
    font-size: 15px;
    color: #fff;
    padding: 0;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px;
}

.quentity_btn input {
    width: 60px;
    text-align: center;
    height: 35px;
    border-radius: 3px;
    border: 1px solid #ddd;
    margin: 0px 5px;
    font-size: 16px;
    font-weight: 400;
    color: var(--colorBlack);
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px;
}

.quentity_btn_area h3 {
    font-size: 20px;
    font-weight: 600;
    margin-left: 10px;
    margin-top: 0;
}

.details_button_area li a {
    margin-right: 10px;
}

.details_button_area .wishlist {
    width: 45px;
    height: 45px;
    text-align: center;
    line-height: 45px;
    margin-right: 0;
    background: var(--gradiantBg);
    color: var(--colorWhite);
    border-radius: 50%;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.details_button_area .wishlist:hover {
    background: var(--gradiantHoverBg);
}

.wsus__menu_description_area .nav {
    background: #faf6f3fa;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 25px;
}

.wsus__menu_description_area .nav .nav-item {
    width: 50%;
    border-right: 1px solid var(--colorPrimary);
}

.wsus__menu_description_area .nav .nav-item:last-child {
    border: 0;
}

.wsus__menu_description_area .nav .nav-item .nav-link {
    width: 100%;
    border-radius: 0;
    text-align: center;
    text-transform: capitalize;
    font-size: 18px;
    font-weight: 600;
    color: var(--colorBlack);
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__menu_description_area .nav .nav-item .nav-link:hover,
.wsus__menu_description_area .nav .nav-item .nav-link.active {
    background: none !important;
    color: var(--colorPrimary);
}

.menu_det_description p {
    margin-top: 25px;
}

.menu_det_description ul,
.menu_det_description ol {
    margin-top: 15px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.menu_det_description ul li,
.menu_det_description ol li {
    font-size: 16px;
    font-weight: 400;
    color: var(--paraColor);
    margin-top: 10px;
    position: relative;
    padding-left: 30px;
    width: 49%;
}

.menu_det_description ul li::after,
.menu_det_description ol li::after {
    position: absolute;
    content: "\f00c";
    color: var(--colorWhite);
    font-family: "Font Awesome 5 Free";
    font-weight: 600;
    top: 2px;
    left: 0;
    font-size: 10px;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    background: var(--colorPrimary);
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
}

.wsus__review_area h4,
.wsus__post_review h4 {
    text-transform: capitalize;
    font-size: 20px;
    font-weight: 600;
}

.wsus__comment .load_more,
.wsus__review_area .load_more {
    float: right;
    text-transform: capitalize;
    color: var(--colorPrimary);
    font-size: 14px;
    font-weight: 600;
    margin-top: 10px;
    border: 1px solid var(--colorPrimary);
    padding: 3px 15px 4px 15px;
    border-radius: 30px;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__comment .load_more:hover,
.wsus__review_area .load_more:hover {
    background: var(--gradiantBg);
    color: var(--colorWhite);
}

.wsus__post_review form {
    background: var(--colorWhite);
    padding: 20px;
    border-radius: 10px;
    margin-top: 20px;
    box-shadow: var(--boxShadow);
}

.wsus__post_review form .rating {
    text-transform: capitalize;
    color: var(--colorBlack);
    font-size: 15px;
}

.wsus__post_review form .rating i {
    color: #ff9933;
    cursor: pointer;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__post_review form .rating i:hover {
    color: var(--colorPrimary);
}

.wsus__post_review form input,
.wsus__post_review form textarea {
    margin-top: 20px;
}

.wsus__post_review form button {
    margin-top: 15px;
}

.wsus__related_menu h2 {
    text-transform: capitalize;
    font-size: 35px;
    font-weight: 800;
    margin-bottom: 20px;
}

.wsus__related_menu .wsus__menu_item {
    margin: 75px 12px 5px 12px;
}


/*============================
    MENU DETAILS END
============================*/


/*======================
    CART VIEW PAGE START
========================*/

.wsus__cart_list {
    background: #fff;
    border-radius: 5px;
    overflow: hidden;
    border: 1px solid #eee;
    border-right: 0;
}

.wsus__cart_list table {
    width: 100%;
}

.wsus__cart_list table tbody tr {
    border-top: 1px solid #eee;
    border-right: 1px solid #eee !important;
    display: flex;
    width: 100%;
    justify-content: space-between;
}

.wsus__cart_list table tbody tr:first-child {
    border-top: 0;
    padding: 0;
}

.wsus__cart_list .wsus__pro_img {
    width: 130px;
    padding: 10px;
}

.wsus__cart_list .wsus__pro_name {
    width: 240px;
}

.wsus__cart_list td.wsus__pro_name {
    flex-direction: column;
}

.wsus__pro_status p {
    text-transform: capitalize;
    color: #08C !important;
}

.wsus__pro_status span {
    text-transform: capitalize;
    color: #dc3545 !important;
    display: inline-block;
}

.wsus__cart_list .wsus__pro_select {
    width: 184px;
}

.wsus__cart_list .wsus__pro_tk,
.wsus__cart_list .wsus__pro_status {
    width: 102px;
}

.wsus__cart_list .wsus__pro_tk h6,
.wsus__cart_list .wsus__pro_status h6 {
    color: var(--colorBlack);
    font-size: 16px;
    font-weight: 600;
}

.wsus__cart_list .wsus__pro_tk h6 del {
    color: #888;
    margin-right: 15px;
}

.wsus__cart_list .wsus__pro_icon {
    width: 95px;
    border-right: none !important;
}

.wsus__cart_list .wsus__pro_icon a {
    text-align: center;
}

.wsus__pro_icon .common_btn {
    font-size: 12px;
    text-transform: capitalize;
    color: #fff !important;
    padding: 7px 10px 5px 10px;
    font-weight: 400;
}

.wsus__pro_icon .common_btn:hover {
    color: #fff;
}

.wsus__pro_icon a {
    color: #0b2c3d;
    font-size: 20px;
    width: 100%;
    text-align: center;
    transition: all linear 0.3s;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
}

.wsus__pro_icon:hover a {
    color: #ed1b35;
}

.wsus__cart_list table tr th,
.wsus__cart_list table tr td {
    background: #faf6f3fa;
    border: none;
    color: var(--colorBlack);
    text-align: center;
    text-transform: capitalize;
    font-size: 16px;
    font-weight: 900;
    padding: 10px !important;
    display: flex;
    flex-direction: column;
    align-items: start;
    justify-content: center;
    border-right: 1px solid #eee;
}

.wsus__cart_list table tr th .clear_all {
    color: var(--colorPrimary);
    padding: 0;
    font-size: 16px;
    font-weight: 900;
}

.wsus__cart_list table tbody tr td a,
.wsus__cart_list table tbody tr td p,
.wsus__cart_list table tbody tr td span {
    font-family: "Roboto", sans-serif;
    color: var(--paraColor);
    text-transform: capitalize;
    text-align: left;
    font-size: 14px;
}

.wsus__cart_list table tbody tr td a {
    color: var(--colorBlack);
    font-size: 16px;
    display: block;
    margin-bottom: 5px;
    width: 100%;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__cart_list table tbody tr td a:hover {
    color: var(--colorPrimary);
}

.wsus__cart_list table tbody tr td p {
    margin-top: 5px;
    width: 100%;
}

.wsus__cart_list table tbody tr td span {
    display: block;
    width: 100%;
    color: var(--colorPrimary);
    font-size: 14px;
}

.wsus__pro_name span {
    text-transform: capitalize;
    color: #0b2c3d;
    font-size: 14px;
}

.cart_summery {
    padding: 0 !important;
    border: 1px solid #ddd !important;
    margin-top: 0 !important;
}

.cart_summery_text {
    padding: 10px;
}

.cart_summery h5 {
    background: #def0ff;
    text-align: center;
    padding: 15px 0px 15px 0px !important;
    border-bottom: 1px solid #ddd !important;
}

.cart_summery .wsus__sub_total {
    font-weight: 400 !important;
    border: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.cart_summery .common_btn {
    width: 100%;
    text-align: center;
    border-radius: 0;
}

.cart_summery .wsus_total_area {
    margin: 20px 0px 7px 0px;
    border-top: 1px solid #ddd;
    padding-top: 18px;
}

.cart_summery .wsus_total_area .wsus__sub_total {
    font-weight: 600 !important;
}

.cart_summery .wsus_total_area .wsus__sub_total span {
    font-weight: 600 !important;
}

.cart_summery .form-check label {
    display: flex;
    justify-content: space-between;
}

.cart_summery .form-check label span {
    color: #0b2c3d;
}

.cart_page_rel_product {
    margin-top: 67px !important;
}

.wsus__cart_list_footer {
    border-radius: 5px;
    background: #def0ff;
}

.wsus__cart_list_footer_top {
    display: flex;
    align-items: stretch;
    flex-direction: column;
    height: 100%;
    justify-content: center;
}

.wsus__cart_list_footer_button form {
    margin-top: 15px;
    position: relative;
    border: 1px solid var(--colorPrimary);
    border-radius: 3px;
    overflow: hidden;
}

.wsus__cart_list_footer_button input {
    width: 100%;
    padding: 10px 20px;
    font-size: 15px;
    font-weight: 400;
    background: #faf6f3fa;
    border: 1px solid #eee;
}

.wsus__cart_list_footer_button button {
    text-transform: capitalize;
    margin-left: 10px;
    padding: 8px 20px;
    text-align: center;
    border-radius: 3px;
    background: var(--gradiantBg);
    color: var(--colorWhite);
    font-size: 14px;
    font-weight: 600;
    position: absolute;
    top: 50%;
    right: 3px;
    transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
}

.wsus__cart_list_footer_button button:hover {
    background: var(--gradiantHoverBg);
}

.wsus__cart_list_footer_button .common_btn i {
    margin-right: 5px;
}

.wsus__cart_list_footer_button {
    background: #fff;
    border-radius: 5px;
    padding: 20px;
    box-shadow: rgb(50 50 93 / 15%) 0px 0px 5px 0px;
}

.wsus__cart_list_footer_button h6 {
    border: none;
    color: var(--colorBlack);
    text-transform: capitalize;
    font-size: 18px;
    font-weight: 600;
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
}

.wsus__cart_list_footer_button p {
    text-transform: capitalize;
    margin-top: 15px;
    display: flex;
    justify-content: space-between;
    font-size: 15px;
}

.wsus__cart_list_footer_button .total {
    border-top: 1px solid #eee;
    padding-top: 15px;
    color: var(--colorBlack);
}

.wsus__cart_list_footer_button .total span {
    font-weight: 600;
    color: var(--colorBlack);
    font-size: 18px;
}

.wsus__cart_list_footer_button a {
    width: 100%;
    text-align: center;
    margin-top: 20px;
    padding: 10px 20px !important;
}

.wsus__cart_list_footer_button a::after,
.wsus__cart_list_footer_button a::before {
    display: none;
}

.wsus__cart_view .cart_empty_text {
    text-transform: capitalize;
    font-size: 30px;
    font-weight: 500;
    color: var(--colorPrimary) !important;
}


/*======================
CART VIEW PAGE END
========================*/


/*======================
CHECKOUT PAGE START
========================*/

.wsus__check_form {
    background: #fff;
    padding: 25px 25px 5px 25px;
    border-radius: 5px;
    box-shadow: rgb(50 50 93 / 15%) 0px 0px 5px 0px;
}

.wsus__checkout_form h5 {
    font-size: 18px;
    font-weight: 600;
    text-transform: capitalize;
    margin-bottom: 15px;
    color: #0b2c3d;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.wsus__checkout_form h5 a {
    border-radius: 3px;
    font-size: 16px;
    font-weight: 600;
    color: var(--colorPrimary);
    border: 1px solid var(--colorPrimary);
    padding: 5px 10px;
    font-family: var(--paraFont);
    background: var(--colorWhite);
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px;
}

.wsus__checkout_form h5 a i {
    margin-right: 5px;
}

.wsus__checkout_form h5 a:hover {
    background: var(--colorPrimary);
    color: var(--colorWhite);
}

.wsus__address_modal .modal-dialog {
    max-width: 700px;
}

.wsus__address_modal .modal-header {
    border: none;
    background: #f6eee7fa;
}

.wsus__address_modal .modal-title {
    text-align: center;
    width: 100%;
    text-transform: capitalize;
    font-weight: 600;
}

.wsus__address_modal .common_btn {
    width: 100%;
    text-align: center;
    padding: 10px 20px !important;
}

.wsus__address_modal .common_btn::after,
.wsus__address_modal .common_btn::before {
    display: none;
}

.wsus__checkout_single_address {
    border: 1px solid #eeeeeea6;
    padding: 20px;
    border-radius: 5px;
    margin-bottom: 20px;
    background: #faf6f3;
    position: relative;
}

.wsus__checkout_single_address .form-check input {
    padding: 0;
    border-color: var(--colorPrimary);
}

.wsus__checkout_single_address .form-check input:checked {
    background-color: var(--colorPrimary);
    border-color: var(--colorPrimary);
}

.wsus__checkout_single_address .form-check label {
    display: block;
    width: 100%;
    cursor: pointer;
}

.wsus__checkout_single_address .form-check label .icon {
    border: 1px solid var(--colorPrimary);
    text-transform: capitalize;
    padding: 2px 15px;
    border-radius: 2px;
    display: inline-block;
    font-size: 14px;
    font-weight: 600;
    color: var(--colorPrimary);
}

.wsus__checkout_single_address .form-check label .icon i {
    margin-right: 5px;
}

.wsus__checkout_single_address .form-check label .address {
    text-transform: capitalize;
    margin-top: 10px;
    display: block;
}

.wsus__check_form .wsus__check_single_form {
    margin-bottom: 20px;
}

.wsus__check_form .nice-select {
    border: 1px solid #eee !important;
    padding: 12px 20px;
    margin-bottom: 20px;
}

.wsus__checkout_single_address ul {
    display: flex;
    position: absolute;
    top: 5px;
    right: 5px;
}

.wsus__checkout_single_address ul li a {
    width: 25px;
    height: 25px;
    line-height: 23px;
    background: var(--colorWhite);
    color: var(--colorPrimary);
    border: 1px solid var(--colorPrimary);
    font-size: 12px;
    text-align: center;
    margin: 3px;
    border-radius: 2px;
    transition: all linear .3s;
    cursor: pointer;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__checkout_single_address ul li a:hover {
    background: var(--colorPrimary);
    color: var(--colorWhite);
}


/*======================
  CHECKOUT PAGE END
========================*/


/*========================
    SIGNIN START
=========================*/

.wsus__signin,
.wsus__signup {
    background-repeat: no-repeat !important;
    background-size: cover !important;
    background-position: center !important;
}

.wsus__signin_overlay,
.wsus__signup_overlay {
    background: #ffffffe8;
}

.wsus__login_area {
    background: #faf6f3fa;
    padding: 50px;
    overflow: hidden;
    border-radius: 5px;
    border: 1px solid #ddd;
}

.wsus__login_area h2 {
    font-weight: 700;
    font-size: 35px;
    color: var(--colorBlack);
    text-transform: capitalize;
    margin-bottom: 5px;
}

.wsus__login_area p {
    color: var(--colorBlack);
    text-transform: capitalize;
    margin-bottom: 25px;
}

.wsus__login_area .wsus__login_imput {
    margin-bottom: 20px;
}

.wsus__login_area form label {
    text-transform: capitalize;
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 5px;
}

.wsus__login_area form button {
    width: 100%;
    text-align: center;
    padding: 10px 25px !important;
}

.wsus__login_area form button::after,
.wsus__login_area form button::before {
    display: none;
}

.wsus__login_check_area {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.wsus__login_area .form-check input {
    padding: 0px;
    border-radius: 3px;
}

.wsus__login_area .form-check input:checked {
    background-color: var(--colorPrimary);
    border-color: var(--colorPrimary);
}

.form-check-input:focus {
    box-shadow: none;
}

.wsus__login_area .form-check label {
    color: var(--paraColor);
    margin: 0px;
    text-transform: capitalize;
    font-size: 16px;
    font-weight: 400;
}

.wsus__login_imput a {
    text-transform: capitalize;
    font-size: 16px;
    font-weight: 400;
    color: var(--colorPrimary);
    transition: all 0.3s linear 0s;
    -webkit-transition: all 0.3s linear 0s;
    -moz-transition: all 0.3s linear 0s;
    -ms-transition: all 0.3s linear 0s;
    -o-transition: all 0.3s linear 0s;
}

.wsus__login_imput a:hover {
    color: var(--colorBlack);
}

.wsus__login_area .or {
    position: relative;
    margin: 20px 0px 40px;
}

.wsus__login_area .or span {
    text-transform: uppercase;
    width: 25px;
    height: 25px;
    display: inline-block;
    background: var(--gradiantBg);
    font-size: 13px;
    text-align: center;
    line-height: 27px;
    color: var(--colorWhite);
    border-radius: 3px;
    position: relative;
    z-index: 1;
}

.wsus__login_area .or::after {
    position: absolute;
    content: "";
    background: var(--colorPrimary);
    width: 98%;
    height: 1px;
    top: 50%;
    right: 0px;
    transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
}

.wsus__login_area ul li a {
    width: 35px;
    height: 35px;
    background: var(--gradiantBg);
    color: var(--colorWhite);
    text-align: center;
    line-height: 35px;
    border-radius: 50%;
    margin-right: 5px;
    font-size: 14px;
    border: 1px solid transparent;
    transition: all 0.3s linear 0s;
    -webkit-transition: all 0.3s linear 0s;
    -moz-transition: all 0.3s linear 0s;
    -ms-transition: all 0.3s linear 0s;
    -o-transition: all 0.3s linear 0s;
}

.wsus__login_area ul li a:hover {
    background: var(--gradiantHoverBg);
}

.wsus__login_area .create_account {
    margin-top: 30px;
    color: var(--paraColor);
    margin-bottom: 0px;
}

.wsus__login_area .create_account a {
    color: var(--colorPrimary);
    text-transform: capitalize;
    font-weight: 500;
}


/* ==================================
    SIGNIN END
=================================== */


/*============================
    PAYMENT PAGE START
============================*/

.wsus__single_payment {
    border: 1px solid var(--colorPrimary);
    border-radius: 5px;
    overflow: hidden;
    padding: 7px;
    background: #faf6f3fa;
    display: block;
    transition: all linear .3s;
    margin-top: 25px;
    height: 110px;
}

.wsus__single_payment img {
    box-shadow: var(--boxShadow);
    border-radius: 4px;
}

.wsus__single_payment:hover {
    background: var(--colorPrimary);
}

.wsus__pay_modal_info p {
    margin-bottom: 20px;
}

.wsus__pay_modal_info ul li {
    font-size: 16px;
    color: var(--paraColor);
    position: relative;
    padding-left: 35px;
    margin: 10px 0px;
}

.wsus__pay_modal_info ul li::after {
    position: absolute;
    content: "\f00c";
    color: var(--colorWhite);
    font-family: "Font Awesome 5 Free";
    font-weight: 600;
    top: 2px;
    left: 0;
    width: 20px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    font-size: 10px;
    border-radius: 50%;
    background: var(--gradiantBg);
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__pay_modal_info input,
.wsus__pay_modal_info textarea,
.wsus__pay_modal_info .nice-select {
    border: 1px solid #eee;
    margin-top: 15px;
    font-weight: 400;
}

.wsus__pay_modal_info .nice-select {
    margin-top: 10px;
}

.wsus__pay_modal_info .nice-select .option {
    font-weight: 400;
    margin: 0;
}

.wsus__pay_modal_info .nice-select .option::after {
    display: none;
}

.wsus__payment_btn_area {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    justify-content: space-between;
    padding-top: 20px;
}

.wsus__payment_btn_area button {
    border-radius: 3px;
    border: none;
    padding: 10px 30px;
    text-transform: capitalize;
}


/*============================
    PAYMENT PAGE END
============================*/


/*============================
    FAQ PAGE START
============================*/

.wsus__faq_area .accordion-item {
    margin-top: 20px;
    border: none;
    border-radius: 10px !important;
    overflow: hidden;
    box-shadow: var(--boxShadow);
}

.wsus__faq_area .accordion-item h2 button {
    background: var(--gradiantBg);
    color: var(--colorWhite);
    text-transform: capitalize;
    font-size: 16px;
    font-weight: 500;
    border: none;
    padding-right: 60px;
}

.wsus__faq_area .accordion-button:focus {
    box-shadow: none;
}

.wsus__faq_area .accordion-body {
    padding: 20px;
}

.wsus__faq_area .accordion-body p {
    border-left: 3px solid var(--colorPrimary);
    padding-left: 15px;
}

.accordion-button::after {
    background-image: url(../images/faq_plus.jpg);
    width: 20px;
    height: 20px;
    border-radius: 50%;
    position: absolute;
    right: 20px;
}

.accordion-button:not(.collapsed)::after {
    background-image: url(../images/faq_minus.jpg);
}

.wsus__details_video h4 {
    font-size: 24px;
    font-weight: 700;
    text-transform: capitalize;
    color: var(--colorBlack);
}

.wsus__faq_form {
    margin-top: 20px;
}

.wsus__faq_form form {
    box-shadow: var(--boxShadow);
    padding: 25px;
    border-radius: 1px;
}

.wsus__faq_form h3 {
    font-size: 24px;
    font-weight: 600;
    text-transform: capitalize;
}

.wsus__faq_form input,
.wsus__faq_form textarea {
    margin-top: 20px;
}

.wsus__faq_form button {
    margin-top: 15px;
}


/*============================
    FAQ PAGE END
============================*/


/* =================================
    TERMS AND CONDITION START
================================= */

.wsus__terms_condition .wsus__career_det_text P {
    margin-bottom: 20px;
}

.wsus__terms_condition h1,
.wsus__terms_condition h2,
.wsus__terms_condition h3,
.wsus__terms_condition h4,
.wsus__terms_condition h5,
.wsus__terms_condition h6 {
    text-transform: capitalize;
    font-weight: 700;
    margin-bottom: 20px;
}

.wsus__terms_condition h1 {
    font-size: 40px;
}

.wsus__terms_condition h2 {
    font-size: 35px;
}

.wsus__terms_condition h3 {
    font-size: 30px;
}

.wsus__terms_condition h4 {
    font-size: 25px;
}

.wsus__terms_condition h5 {
    font-size: 20px;
}

.wsus__terms_condition ul,
.wsus__terms_condition ol {
    margin-bottom: 20px;
}

.wsus__terms_condition ul li,
.wsus__terms_condition ol li {
    color: var(--paraColor);
    margin-top: 15px;
    padding-left: 25px;
    position: relative;
}

.wsus__career_det_text ul li::after,
.wsus__career_det_text ol li::after {
    position: absolute;
    content: "\f30b";
    font-family: 'Font Awesome 5 Free';
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: var(--colorPrimary);
    left: 0;
    top: 1px;
    font-size: 16px;
    font-weight: 900;
}


/* =================================
    TERMS AND CONDITION END
================================= */


/*============================
    404 page START
============================*/

.wsus__404 {
    height: 100vh;
    padding-top: 130px;
}

.wsus__404 div {
    height: 100%;
}

.wsus__404_text {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

.wsus__404_text img {
    height: 270px !important;
    width: 460px !important;
}

.wsus__404_text h2 {
    text-transform: capitalize;
    font-size: 40px;
    font-weight: 800;
    text-align: center;
    margin-top: 30px;
}

.wsus__404_text p {
    margin: 10px 0px 20px 0px;
    font-size: 18px;
    text-align: center;
}


/*============================
    404 page END
============================*/


/*============================
    TESTIMONIAL PAGE START
============================*/

.wsus__testimonial_page .wsus__single_testimonial {
    margin: 25px 0px 0px 0px;
}


/*============================
    TESTIMONIAL PAGE END
============================*/


/* ===========================
    DASHBOARD START
============================== */

.wsus__dashboard_area {
    border: 1px solid rgb(233, 242, 255);
    border-radius: 5px;
    overflow: hidden;
}

.wsus__dashboard_menu {
    background: #faf6f3fa;
    height: 100%;
    overflow: hidden;
}

.dasboard_header {
    margin-bottom: 20px;
    padding: 30px 0px;
    text-align: center;
}

.dasboard_header .dasboard_header_img {
    border-radius: 50%;
    border: 5px solid var(--colorWhite);
    box-shadow: var(--boxShadow);
    width: 140px;
    height: 140px;
    position: relative;
    margin: 0 auto;
}

.dasboard_header .dasboard_header_img img {
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
}

.dasboard_header .dasboard_header_img label {
    display: block;
    position: absolute;
    bottom: 0;
    right: 5px;
    font-size: 14px;
    /* border: 1px solid #ddd; */
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border-radius: 50%;
    background: var(--colorWhite);
    box-shadow: var(--boxShadow);
    cursor: pointer;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.dasboard_header .dasboard_header_img label:hover {
    background: var(--colorPrimary);
    color: var(--colorWhite);
}

.dasboard_header h2 {
    font-size: 18px;
    font-weight: 900;
    color: var(--colorBlack);
    text-transform: capitalize;
    margin-bottom: 5px;
    margin-top: 25px;
    text-align: center;
}

.dasboard_header p {
    margin: 0px;
}

.wsus__dashboard_menu button,
.wsus__dashboard_menu a {
    text-align: left;
    font-size: 16px;
    font-weight: 500;
    position: relative;
    transition: all 0.3s linear 0s;
    background: none !important;
    color: var(--colorBlack) !important;
    border-radius: 0px !important;
    padding: 0px 0px !important;
    -webkit-transition: all 0.3s linear 0s;
    -moz-transition: all 0.3s linear 0s;
    -ms-transition: all 0.3s linear 0s;
    -o-transition: all 0.3s linear 0s;
    text-transform: capitalize;
}

.wsus__dashboard_menu button span,
.wsus__dashboard_menu a span {
    display: inline-block;
    transition: all 0.3s linear 0s;
    -webkit-transition: all 0.3s linear 0s;
    -moz-transition: all 0.3s linear 0s;
    -ms-transition: all 0.3s linear 0s;
    -o-transition: all 0.3s linear 0s;
    width: 50px;
    height: 50px;
    background: var(--gradiantBg);
    color: var(--colorWhite) !important;
    text-align: center;
    line-height: 50px;
    margin-right: 10px;
}

.nav-pills .nav-link.active,
.nav-pills .nav-link:hover {
    background: #292b750a !important;
}

.wsus__dashboard_menu button.active::after,
.wsus__dashboard_menu button.a::after {
    position: absolute;
    content: "";
    background: var(--gradiantBg);
    width: 8px;
    height: 50px;
    top: 0;
    right: -3px;
    border-radius: 20px;
    transition: all 0.3s linear 0s;
    -webkit-transition: all 0.3s linear 0s;
    -moz-transition: all 0.3s linear 0s;
    -ms-transition: all 0.3s linear 0s;
    -o-transition: all 0.3s linear 0s;
}

.wsus__dashboard_content {
    padding: 25px 25px 25px 0px;
}

.wsus_dashboard_body h3 {
    font-size: 24px;
    font-weight: 700;
    color: var(--colorBlack);
    text-transform: capitalize;
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
}

.wsus_dashboard_body h3 .dash_add_new_address {
    border-radius: 30px;
    padding: 8px 15px;
    background: var(--gradiantBg);
    color: var(--colorWhite) !important;
    cursor: pointer;
    font-size: 14px;
    text-transform: capitalize;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus_dashboard_body h3 .dash_add_new_address:hover {
    background: var(--gradiantHoverBg);
}

.wsus__dsahboard_overview_item {
    background: #faf6f3fa;
    padding: 30px;
    text-align: center;
    border-radius: 10px;
    position: relative;
    border: 1px solid #eee;
    margin-bottom: 25px;
}

.wsus__dsahboard_overview_item .icon {
    display: inline-block;
    width: 50px;
    height: 50px;
    line-height: 50px;
    text-align: center;
    background: orange;
    border-radius: 50%;
    font-size: 20px;
    color: var(--colorWhite);
}

.wsus__dsahboard_overview_item h4 {
    text-transform: capitalize;
    font-size: 16px;
    font-weight: 600;
    color: orange;
    margin-top: 20px;
    font-family: var(--paraFont);
    text-align: center;
}

.wsus__dsahboard_overview_item h4 span {
    font-size: 18px;
    font-weight: 600;
    color: orange;
    font-family: var(--paraFont);
    margin-left: 5px;
}

.wsus__dsahboard_overview_item.red .icon {
    background: var(--colorPrimary);
}

.wsus__dsahboard_overview_item.red h4,
.wsus__dsahboard_overview_item.red h4 span {
    color: var(--colorPrimary);
}

.wsus__dsahboard_overview_item.green .icon {
    background: #4caf50;
}

.wsus__dsahboard_overview_item.green h4,
.wsus__dsahboard_overview_item.green h4 span {
    color: #4caf50;
}

.wsus_dash_personal_info {
    background: #faf6f3fa;
    border-radius: 5px;
    padding: 30px;
}

.wsus_dash_personal_info h4 {
    font-size: 18px;
    font-weight: 700;
    text-transform: capitalize;
    color: var(--colorBlack);
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
}

.wsus_dash_personal_info h4 a {
    font-size: 14px;
    background: var(--gradiantBg);
    color: var(--colorWhite);
    font-weight: 600;
    padding: 4px 15px;
    border-radius: 30px;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
    cursor: pointer;
}

.wsus_dash_personal_info h4 a span {
    color: var(--colorWhite);
}

.wsus_dash_personal_info h4 a:hover {
    background: var(--gradiantHoverBg);
}

.wsus_dash_personal_info h4 a .cancel {
    display: none;
}

.wsus_dash_personal_info p {
    color: var(--colorBlack);
    margin-top: 20px;
    display: flex;
    justify-content: start;
    margin-bottom: 0 !important;
}

.wsus_dash_personal_info p span {
    display: inline-block;
    min-width: 120px;
    font-weight: 500;
    color: var(--colorBlack);
}

.wsus_dashboard_new_address,
.wsus_dashboard_edit_address {
    background: #faf6f3fa;
    border-radius: 5px;
    padding: 30px;
    display: none;
}

.wsus_dashboard_new_address h4,
.wsus_dashboard_edit_address h4 {
    text-transform: capitalize;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 15px;
}

.wsus_dashboard_new_address .wsus__check_single_form,
.wsus_dashboard_edit_address .wsus__check_single_form {
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
}

.wsus_dashboard_new_address .check_area,
.wsus_dashboard_edit_address .check_area {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}

.wsus_dashboard_new_address .form-check,
.wsus_dashboard_edit_address .form-check {
    margin-right: 20px;
    background: var(--colorWhite);
    border: 1px solid #ddd;
    padding: 10px 30px 10px 40px;
    border-radius: 5px;
}

.wsus_dashboard_new_address .form-check label,
.wsus_dashboard_edit_address .form-check label {
    text-transform: capitalize;
    font-size: 16px;
    font-weight: 400;
    color: var(--colorBlack);
    display: block;
    width: 100%;
}

.wsus_dashboard_new_address .form-check input,
.wsus_dashboard_edit_address .form-check input {
    padding: 0;
}

.wsus_dashboard_new_address .form-check input:checked,
.wsus_dashboard_edit_address .form-check input:checked {
    background-color: var(--colorPrimary);
    border-color: var(--colorPrimary);
}

.wsus_dashboard_new_address button,
.wsus_dashboard_edit_address button {
    padding: 8px 20px !important;
}

.wsus_dashboard_new_address button::after,
.wsus_dashboard_new_address button::before,
.wsus_dashboard_edit_address button::after,
.wsus_dashboard_edit_address button::before {
    display: none;
}

.address_body.show_new_address .wsus_dashboard_existing_address {
    display: none;
}

.address_body.show_new_address .wsus_dashboard_new_address {
    display: block;
}

.address_body.show_new_address .dash_add_new_address,
.address_body.show_edit_address .dash_add_new_address {
    display: none;
}

.address_body.show_edit_address .wsus_dashboard_existing_address {
    display: none;
}

.address_body.show_edit_address .wsus_dashboard_edit_address {
    display: block;
}

.wsus_dashboard_existing_address .form-check {
    padding-left: 0;
}

.wsus_dashboard_existing_address .form-check label {
    cursor: default;
}

.wsus_dashboard_order table {
    border-radius: 5px;
    overflow: hidden;
    margin: 0px;
    background: #faf6f3fa;
}

.wsus_dashboard_order table tr {
    border-color: transparent;
    display: flex;
    border-top: 1px solid #ddd;
}

.wsus_dashboard_order table tr th {
    background: var(--colorPrimary);
    text-transform: capitalize;
    font-size: 18px;
    font-weight: 500;
    color: var(--colorWhite) !important;
}

.wsus_dashboard_order table tr th,
.wsus_dashboard_order table tr td {
    text-align: center;
    padding: 10px 0px;
    width: 20%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.wsus_dashboard_order table tr td h5 {
    color: var(--colorBlack);
    font-size: 16px;
    font-weight: 500;
}

.wsus_dashboard_order table tr td p {
    text-transform: capitalize;
    margin: 0px;
}

.wsus_dashboard_order table tr td a {
    font-size: 13px;
    font-weight: 400;
    background: var(--colorPrimary);
    color: var(--colorWhite);
    text-transform: capitalize;
    transition: all 0.3s linear 0s;
    border: 1px solid var(--colorPrimary);
    padding: 5px 10px 3px 10px;
    border-radius: 3px;
    cursor: pointer;
}

.wsus_dashboard_order table tr td a:hover {
    color: var(--colorPrimary);
    background: var(--colorWhite);
}

.wsus_dashboard_order table tr td span {
    border-radius: 6px;
    padding: 7px 0px;
    width: 130px;
    display: inline-block;
    text-align: center;
    text-transform: capitalize;
}

.wsus_dashboard_order table tr td .complete {
    background: rgb(0 191 140 / 9%);
    color: rgb(0, 191, 140);
}

.wsus_dashboard_order table tr td .cancel {
    background: rgba(254, 58, 96, 0.11);
    color: rgb(254, 58, 96);
}

.wsus_dashboard_order table tr td .active {
    background: #292b751c;
    color: var(--colorPrimary);
}

.wsus_dashboard_body .wsus__dashoard_wishlist {
    margin-top: -20px;
}

.dashboard_review .wsus__single_review {
    margin-top: 20px;
}

.wsus__dashboard .modal {
    background: rgba(0, 0, 0, 0.467);
    z-index: 99999;
}

.wsus__dashboard .modal-dialog {
    margin: 0px 0px 10px;
    position: absolute;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    -webkit-transform: translate(-50%, -50%) !important;
    -moz-transform: translate(-50%, -50%) !important;
    -ms-transform: translate(-50%, -50%) !important;
    -o-transform: translate(-50%, -50%) !important;
}

.wsus__dashboard .modal-content {
    padding: 20px;
    border: none;
    width: 500px;
    background: rgb(233, 247, 250);
}

.wsus__dashboard .modal-header {
    border: 0px;
}

.wsus__dashboard .modal-title {
    font-size: 18px;
    font-weight: 700;
    width: 100%;
}

.wsus__dashboard .modal-body {
    text-align: center;
}

.wsus__dashboard .modal-body img {
    margin: 0px auto;
    border-radius: 50%;
    width: 150px !important;
    height: 150px !important;
    margin-bottom: 20px;
}

.wsus__dashboard p {
    margin-bottom: 5px;
}

.wsus__dashboard p b {
    font-size: 18px;
    font-weight: 900;
    color: var(--colorBlack);
    display: block;
}

.wsus__dashboard .modal-content p,
.wsus__dashboard .modal-content b {
    text-align: center;
}

.wsus__dashboard .modal-footer {
    justify-content: space-between;
    border: 0px;
}

.wsus__dashboard .modal-footer button {
    border-radius: 5px;
    padding: 10px 20px;
    font-size: 18px;
    font-weight: 500;
    text-transform: capitalize;
    width: 47%;
    text-align: center;
}

.wsus__dashboard .modal-footer .del_btn {
    background: rgb(245, 249, 255);
    transition: all 0.3s linear 0s;
    -webkit-transition: all 0.3s linear 0s;
    -moz-transition: all 0.3s linear 0s;
    -ms-transition: all 0.3s linear 0s;
    -o-transition: all 0.3s linear 0s;
    background: red;
    color: var(--colorWhite);
}

.wsus__dashboard .modal-footer .del_btn:hover {
    background: #c02b2b;
}

.wsus__payment_modal {
    z-index: 99999;
}

.wsus__invoice .go_back {
    background: var(--gradiantBg);
    border-radius: 30px;
    padding: 8px 15px;
    font-size: 14px;
    text-transform: capitalize;
    color: var(--colorWhite) !important;
    font-weight: 400;
    cursor: pointer;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__invoice .go_back i {
    margin-right: 10px;
}

.wsus__invoice .go_back:hover {
    background: var(--gradiantHoverBg);
}

.wsus__track_order {
    background: var(--colorWhite);
    border-radius: 5px;
    padding: 30px;
    margin-top: 30px;
}

.wsus__track_order ul {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.wsus__track_order ul li {
    width: 20%;
    position: relative;
    font-size: 16px;
    font-weight: 500;
    color: var(--colorBlack);
    text-transform: capitalize;
    padding-top: 25px;
    text-align: center;
    margin-top: 22px;
}

.wsus__track_order ul li::after {
    position: absolute;
    content: "";
    width: 100%;
    height: 3px;
    background: #eee;
    top: 0;
    left: 0;
    z-index: 1;
}

.wsus__track_order ul li::before {
    position: absolute;
    content: "\f1ce";
    color: var(--colorPrimary);
    font-family: "Font Awesome 5 Free";
    font-size: 16px;
    font-weight: 600;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 50%;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    background: #eee;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    z-index: 2;
}

.wsus__track_order ul li:nth-child(2)::before {
    content: "\f00c";
}

.wsus__track_order ul li:nth-child(3)::before {
    content: "\f291";
}

.wsus__track_order ul li:nth-child(4)::before {
    content: "\f0d1";
}

.wsus__track_order ul li:nth-child(5)::before {
    content: "\f466";
}

.wsus__track_order ul li.active::after {
    background: #4caf50;
}

.wsus__track_order ul li.active::before {
    background: #4caf50;
    color: var(--colorWhite);
}

.delivary_time {
    text-transform: capitalize;
    font-size: 18px;
    font-weight: 500;
    margin: 35px 0px 0px 0px;
    color: var(--colorBlack);
    background: #f4e9e9;
    padding: 2px 20px;
    border-radius: 3px;
}

.wsus__invoice_header {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin: 30px 0px;
}

.wsus__invoice_header .header_address {
    max-width: 40%;
}

.wsus__invoice_header .header_address h4 {
    text-transform: capitalize;
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 5px;
}

.wsus__invoice_header .header_address b {
    font-weight: 500;
    font-size: 16px;
    font-family: var(--headingFont);
    text-transform: capitalize;
    width: 95px;
}

.wsus__invoice_header .header_address p {
    display: flex;
    flex-wrap: wrap;
}

.wsus__invoice_header p span {
    color: var(--colorPrimary);
}

.wsus__invoice_header h5 {
    text-transform: capitalize;
    font-size: 30px;
    font-weight: 800;
}

.wsus__invoice table {
    border: 0.5px solid #ddd;
    margin: 0;
    border-bottom: 0.5px solid #ddd;
}

.wsus__invoice table tr {
    border: transparent;
    border-right: 0;
}

.wsus__invoice table tr th {
    background: var(--colorPrimary);
    border: none;
    font-weight: 700;
    font-size: 16px;
    text-transform: capitalize;
    color: var(--colorWhite) !important;
}

.wsus__invoice table tr th,
.wsus__invoice table tr td {
    padding: 10px;
    border-right: 0.5px solid #ddd;
    text-align: center;
}

.wsus__invoice table tr:last-child td {
    border-bottom: .5px solid #ddd;
}

.wsus__invoice table tr td {
    color: var(--colorBlack);
    vertical-align: middle;
}

.wsus__invoice table tr td p {
    color: var(--colorBlack);
    margin: 0;
    font-weight: 500;
}

.wsus__invoice table tr td .size,
.wsus__invoice table tr td .coca_cola,
.wsus__invoice table tr td .coca_cola2 {
    display: block;
    color: var(--colorBlack);
    text-transform: capitalize;
}

.wsus__invoice table tr td .size {
    color: var(--colorPrimary);
}

.wsus__invoice table tr td b {
    font-weight: 500;
}

.wsus__invoice .sl_no {
    min-width: 60px;
}

.wsus__invoice .package {
    width: 40%;
    text-align: left;
}

.wsus__invoice .price,
.wsus__invoice .qnty,
.wsus__invoice .total {
    width: 20%;
}

.wsus__invoice table tfoot {
    border-bottom: .5px solid #ddd;
}

.wsus__invoice table tfoot tr td {
    border-bottom: .5px solid #ddd;
}

.wsus__invoice table tfoot tr td b {
    margin: 0;
    border: none;
    font-weight: 700;
    font-size: 16px;
    text-transform: capitalize;
    display: block;
    text-align: center;
}

.wsus__invoice table tfoot .coupon b,
.wsus__invoice table tfoot .coast b {
    font-weight: 500 !important;
}

.wsus__invoice table tfoot .coupon b {
    color: var(--colorPrimary);
}

.wsus_dashboard_body .wsus__invoice {
    display: none;
    background: #faf6f3fa;
    border-radius: 5px;
    padding: 30px;
    overflow: hidden;
}

.dashboard_review .wsus__comment {
    background: #faf6f3fa;
    box-shadow: none;
}

.wsus_dash_personal_info_edit {
    background: none;
    box-shadow: none;
    display: none;
}

.wsus_dash_personal_info.show .personal_info_text {
    display: none;
}

.wsus_dash_personal_info.show .wsus_dash_personal_info_edit {
    display: block;
}

.wsus_dash_personal_info.show .cancel {
    display: block;
}

.wsus_dash_personal_info.show .edit {
    display: none;
}

.wsus__invoice .print_btn {
    padding: 10px 25px !important;
    float: right;
    margin-top: 35px;
}

.wsus__invoice .print_btn::after,
.wsus__invoice .print_btn::before {
    display: none;
}

.wsus__invoice .print_btn i {
    margin-right: 5px;
}

.wsus__review_input .comment_input {
    background: #faf6f3fa;
    box-shadow: none;
}

.wsus__change_password .wsus__comment_imput_single {
    margin-top: 20px;
}


/*=======================
    DASHBOARD END
========================*/


/*============================
    HOME 2 START
============================*/


/* menu start */

.home_2 .main_menu {
    top: 20px;
    background: transparent;
    border: 0;
}

.home_2 .main_menu .navbar-nav .nav-item .nav-link {
    color: var(--colorWhite);
}

.home_2 .main_menu .reserv_btn {
    line-height: initial;
    padding: 10px 25px !important;
    margin-left: 15px;
}

.home_2 .main_menu .reserv_btn::after,
.home_2 .main_menu .reserv_btn::before {
    display: none;
}

.home_2 .main_menu .menu_icon li a {
    color: var(--colorWhite);
    width: 35px;
    height: 35px;
    line-height: 35px;
    text-align: center;
    border-radius: 50%;
    background: var(--gradiantBg);
    padding: 0 !important;
}

.home_2 .main_menu .menu_icon li a:hover {
    background: var(--gradiantHoverBg);
}

.home_2 .main_menu .menu_icon li a span {
    background: var(--gradiantBg);
    top: -8px;
    right: -10px;
}

.home_2 .menu_fix {
    background: var(--colorWhite);
    border-bottom: 1px solid #eee;
}

.home_2 .menu_fix .navbar-nav .nav-item .nav-link {
    color: var(--colorBlack);
}


/* menu end */


/* bannner 2 start */

.wsus__banner2 {
    overflow-x: hidden;
}

.wsus__single_banner2 {
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-size: cover !important;
}

.wsus__single_banner2_overlay {
    background: #00000030;
    height: 100vh;
}

.wsus__single_banner2_overlay div {
    height: 100%;
}

.wsus__banner_text2 {
    padding: 0;
    padding-top: 75px;
}

.wsus__banner_text2 h1,
.wsus__banner_text2 h3,
.wsus__banner_text2 p {
    color: var(--colorWhite);
}

.wsus__banner2 .prevArrow,
.wsus__banner2 .nextArrow {
    position: absolute;
    top: 50%;
    right: 30px;
    transform: translateY(-50%);
    width: 60px;
    height: 60px;
    background: var(--colorWhite);
    color: var(--colorPrimary);
    line-height: 60px;
    text-align: center;
    border-radius: 50%;
    font-size: 20px;
    opacity: .5;
    cursor: pointer;
    z-index: 1;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
}

.wsus__banner2 .prevArrow {
    right: auto;
    left: 30px;
}

.wsus__banner2 .prevArrow:hover,
.wsus__banner2 .nextArrow:hover {
    opacity: 1;
}


/* banner 2 end */


/* why choose 2 start */

.wsus__why_choose2 {
    background: none;
    padding: 0;
}

.wsus__why_choose2 .wsus__choose_single {
    background: #faf6f3fa;
    padding: 25px;
    border-radius: 10px;
    position: relative;
    border: 1px solid #eee;
}

.wsus__why_choose2 .wsus__choose_single .icon {
    box-shadow: var(--boxShadow);
    border: 5px solid #fff;
    line-height: 49px;
    font-size: 25px;
}

.wsus__why_choose2 .wsus__choose_single .text h3 {
    color: var(--colorBlack);
}

.wsus__why_choose2 .wsus__choose_single .text p {
    color: var(--paraColor);
}


/* why choose 2 end */


/* offer item end */

.wsus__offer_item2 {
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-size: cover !important;
}

.wsus__offer_item2_overlay {
    background: rgb(248 248 248);
}

.wsus__section_heading2 h4 {
    margin-top: 15px;
}

.wsus__offer_item2 .wsus__offer_item_single {
    background: var(--gradiantBg);
    position: relative;
    margin: 25px 12px 0px 37px;
    padding: 55px 20px 20px 20px;
    text-align: center;
    border-radius: 10px;
    border: none;
}

.wsus__offer_item2 .wsus__offer_item_single .img {
    width: 130px;
    height: 130px;
    border-radius: 50%;
    padding: 5px;
    background: var(--colorWhite);
    box-shadow: var(--boxShadow);
    position: absolute;
    top: -25px;
    left: -25px;
    border: 1px solid #eee;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__offer_item2 .wsus__offer_item_single ul {
    margin-top: 15px;
    justify-content: center;
}

.wsus__offer_item2 .wsus__offer_item_single ul li a {
    font-size: 14px;
    background: var(--colorWhite);
    color: var(--colorPrimary);
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
}

.wsus__offer_item2 .wsus__offer_item_single ul li a:hover {
    color: var(--colorBlack);
}

.wsus__offer_item2 .wsus__offer_item_single span,
.wsus__offer_item2 .wsus__offer_item_single .title,
.wsus__offer_item2 .wsus__offer_item_single p {
    color: var(--colorWhite);
    text-align: center;
}

.wsus__offer_item2 .wsus__offer_item_single:hover .img {
    border-color: var(--colorBlack);
    background: var(--colorBlack);
}

.wsus__offer_item2 .wsus__offer_item_single .title:hover {
    color: var(--colorBlack);
}


/* offer item end */


/* menu 2 start */

.wsus__menu2 .menu_filter button {
    padding: 8px 30px;
    margin: 5px 2px 0px 3px;
    border-radius: 2px;
}

.wsus__menu2 .wsus__menu_item {
    margin-top: 25px;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--boxShadow);
    border-bottom: 5px solid var(--colorPrimary);
}

.wsus__menu2 .wsus__menu_item .wsus__menu_item_text {
    box-shadow: none;
    border-radius: 0;
    padding: 25px 25px 25px 25px;
    border-bottom: none;
}

.wsus__menu2 .wsus__menu_item img {
    height: 220px !important;
}

.wsus__menu2 .wsus__menu_item .category {
    text-transform: capitalize;
    font-size: 16px;
    color: var(--colorPrimary);
    font-family: var(--cursiveFont);
    position: absolute;
    top: 5px;
    left: 5px;
    background: var(--colorWhite);
    padding: 3px 15px;
    box-shadow: var(--boxShadow);
    border-radius: 5px;
}

.wsus__menu2 .wsus__menu_item_text ul li a {
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
}


/* menu 2 end */


/* video start */

.home_2 .wsus__about_video_bg {
    border-radius: 0;
}


/* video end */


/* download app 2 start */

.wsus__download2 .wsus__download_img {
    height: 450px;
}

.wsus__download2 .wsus__download_bg {
    box-shadow: none;
    background-repeat: repeat !important;
    background-size: contain !important;
    border: none;
}

.wsus__download2 .wsus__download_overlay {
    background: #faf6f385;
}

.wsus__download2 .wsus__download_text h4,
.wsus__download2 .wsus__download_text h2,
.wsus__download2 .wsus__download_text span,
.wsus__download2 .wsus__download_text p {
    text-align: center;
}

.wsus__download2 .wsus__download_text span {
    margin: 0 auto;
}


/* download app 2 end */


/* blog 2 start */

.wsus__blog2 .wsus__blog_overlay {
    background: rgb(255 255 255 / 93%);
}

.wsus__blog2 .wsus__single_blog_img {
    height: 250px;
}

.wsus__blog2 .wsus__single_blog_text {
    background: var(--colorWhite) !important;
    position: inherit;
}

.wsus__blog2 .wsus__single_blog:hover ul li,
.wsus__blog2 .wsus__single_blog:hover .title {
    color: var(--colorBlack);
}

.wsus__blog2 .wsus__single_blog .title:hover {
    color: var(--colorPrimary);
}

.wsus__blog2 .wsus__single_blog_text ul li {
    font-size: 14px;
}

.wsus__blog2 .wsus__single_blog_text ul li i {
    width: auto;
    height: auto;
    line-height: initial;
    font-size: 14px;
    background: var(--colorWhite);
    color: var(--colorPrimary);
    margin-right: 10px;
}


/* blog 2 end */


/* tean 2 start */

.wsus__team2 .wsus__single_team2 {
    margin: 25px 12px 10px 12px;
}

.wsus__single_team2 {
    box-shadow: var(--boxShadow);
    border-radius: 10px;
    background: var(--colorWhite);
    text-align: center;
    margin-top: 25px;
    overflow: hidden;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__single_team_img2 {
    height: 330px;
    position: relative;
    overflow: hidden;
}

.wsus__single_team_img2 img {
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__single_team_img2 ul {
    position: absolute;
    width: 100%;
    bottom: -40px;
    left: 0;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__single_team_img2 ul li a {
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background: var(--gradiantBg);
    color: var(--colorWhite);
    font-size: 14px;
    margin: 0px 5px;
    border-radius: 50%;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
}

.wsus__single_team_img2 ul li a:hover {
    background: var(--gradiantHoverBg);
}

.wsus__single_team_text2 {
    padding: 20px 25px;
}

.wsus__single_team_text2 h4 {
    text-transform: capitalize;
    color: var(--colorBlack);
    font-size: 24px;
    font-family: var(--headingFont);
    font-weight: 600;
    display: block;
    text-align: center;
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__single_team_text2 p {
    text-transform: capitalize;
    margin-top: 5px;
    text-align: center;
}

.wsus__single_team2:hover ul {
    bottom: 10px;
}

.wsus__single_team2:hover .wsus__single_team_img2 img {
    transform: scale(1.1);
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    -o-transform: scale(1.1);
}


/* tean 2 end*/


/* testimonial 2 start */

.wsus__single_testimonial2 {
    background: #faf6f3fa;
    border: 1px solid #eee;
    padding: 40px;
    border-radius: 10px;
    position: relative;
    overflow: hidden;
    margin: 5px 12px 0px;
}

.wsus__testimonial_product2 {
    width: 160px;
    height: 160px;
    border-radius: 50%;
    padding: 5px;
    box-shadow: var(--boxShadow);
}

.wsus__testimonial_text2 {
    max-width: 65%;
    padding-left: 30px;
    margin-left: 30px;
    border-left: 1px dashed var(--colorPrimary);
}

.wsus__testimonial_text2 .client_img {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    overflow: hidden;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    box-shadow: var(--boxShadow);
    border: 5px solid var(--colorWhite);
}

.wsus__testimonial_text2 h4 {
    text-transform: capitalize;
    font-size: 20px;
    font-weight: 600;
    margin: 5px 0px;
}

.wsus__testimonial_text2 p {
    text-transform: uppercase;
}

.wsus__testimonial_text2 .rating {
    color: #ff9933;
    font-size: 14px;
    text-align: left;
    display: block;
    margin-top: 5px;
}

.wsus__single_testimonial_body2 {
    margin-top: 25px;
}

.wsus__single_testimonial_body2 p {
    text-align: center;
}

.wsus__single_testimonial2::after {
    position: absolute;
    content: "";
    background: url(../images/quot.png);
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    width: 80px;
    height: 80px;
    bottom: 10px;
    right: 10px;
    z-index: 1;
    opacity: .2;
}

.wsus__testimonial2 .nextArrow,
.wsus__testimonial2 .prevArrow {
    width: 40px;
    height: 40px;
    background: var(--gradiantBg);
    line-height: 40px;
    text-align: center;
    color: var(--colorWhite);
    border-radius: 50%;
    position: absolute;
    top: 50%;
    right: -7px;
    transform: translateY(-50%);
    opacity: .2;
    z-index: 1;
    cursor: pointer;
    transition: all linear .3s;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
}

.wsus__testimonial2 .prevArrow {
    right: auto;
    left: -7px;
}

.wsus__testimonial2:hover .nextArrow,
.wsus__testimonial2:hover .prevArrow {
    opacity: 1;
}


/* testimonial 2 end */


/* brand 2 start */

.wsus__brand2 .wsus__brand_overlay {
    background: var(--colorWhite);
}

.wsus__brand2 .wsus__single_brand {
    background: var(--gradiantBg);
    border-radius: 10px;
    margin: 0px 12px;
}


/* brand 2 end */


/* footer 2 start */

.footer2 .footer_overlay {
    background: #000000d9;
}

.wsus__subscribe {
    border-bottom: 1px solid #eeeeee29;
    padding: 40px 0px;
}

.wsus__subscribe_text h3 {
    font-size: 40px;
    color: var(--colorWhite);
    text-transform: capitalize;
    font-weight: 900;
    margin-bottom: 10px;
}

.wsus__subscribe_text p {
    color: var(--colorWhite);
    font-size: 18px;
}

.subscribe_form {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
}

.subscribe_form form {
    position: relative;
    border-radius: 30px;
    overflow: hidden;
    width: 100%;
}

.subscribe_form input {
    padding: 20px 30px;
    border: none;
}

.subscribe_form button {
    position: absolute;
    top: 50%;
    right: 12px;
    transform: translateY(-50%);
    padding: 10px 25px !important;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
}

.subscribe_form button i {
    margin-left: 10px;
}

.subscribe_form button::after,
.subscribe_form button::before {
    display: none;
}

.footer2 .wsus__footer_bottom {
    background: transparent;
    border-top: 1px solid #eeeeee29;
    padding: 20px 0px;
}

.footer2 .wsus__footer_bottom_text p b {
    margin: 0px 5px;
}

.footer2 .wsus__footer_bottom_text ul li a {
    color: var(--colorWhite) !important;
    background: var(--gradiantBg) !important;
    margin-left: 10px;
    margin-right: 0;
}

.footer2 .wsus__footer_content .info {
    margin-top: 10px;
}

.footer2 .wsus__footer_content span {
    margin: 20px 0px;
}

.latest_post li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
}

.latest_post li .img {
    width: 80px;
    height: 80px;
    overflow: hidden;
}

.latest_post li .text {
    width: 75%;
}

.latest_post li .text a {
    font-size: 18px;
    font-weight: 600;
    padding: 0 !important;
    margin: 0 !important;
    margin-bottom: 7px !important;
    font-family: var(--headingFont);
    transition: all linear .3s;
    -webkit-transition: all linear .3s;
    -moz-transition: all linear .3s;
    -ms-transition: all linear .3s;
    -o-transition: all linear .3s;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.latest_post li .text a::after {
    display: none;
}

.latest_post li .text p {
    color: var(--colorWhite);
    text-transform: capitalize;
}

.latest_post li .text p i {
    color: var(--colorPrimary);
    margin-right: 10px;
}

.latest_post li .text a:hover {
    color: var(--colorPrimary) !important;
}


/* footer 2 end */


/*============================
    HOME 2 END
============================*/


/*============================
    SEARCH MENU START
============================*/

.wsus__search_menu_form {
    padding: 20px;
    background: #faf6f3fa;
    border: 1px solid #eee;
    border-radius: 5px;
}

.wsus__search_menu_form .nice-select {
    padding: 12px 20px;
}

.wsus__search_menu_form button {
    width: 100%;
    text-align: center;
    padding: 14px 20px !important;
    border-radius: 4px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -ms-border-radius: 4px;
    -o-border-radius: 4px;
}

.wsus__search_menu_form button::after,
.wsus__search_menu_form button::before {
    display: none;
}


/*============================
    SEARCH MENU END
============================*/


/*============================
    SIGN IN START
============================*/

.wsus__login_area .form-check label {
    padding-right: 25px;
}

.wsus__login_area ul li a {
    margin-right: 0;
    margin-left: 5px;
}


/*============================
    SIGN IN END
============================*/

.reservation_list .sn {
    width: 15%;
    min-width: 80px;
}

.reservation_list .time {
    width: 45%;
    min-width: 280px;
}

.reservation_list .person {
    width: 20%;
    min-width: 100px;
}

.reservation_list .status {
    width: 20%;
    min-width: 140px;
}

.reservation_input.datepicker {
    padding: 8px 20px;
}

.wsus__cart_view .check_area {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}

.wsus__cart_view .check_area .form-check {
    margin-right: 20px;
    background: var(--colorWhite);
    border: 1px solid #ddd;
    padding: 10px 30px 10px 40px;
    border-radius: 5px;
}

.wsus__cart_view .check_area .form-check input {
    padding: 0;
}

.wsus__cart_view .check_area .form-check input:checked {
    background-color: var(--colorPrimary);
    border-color: var(--colorPrimary);
}

.wsus__cart_view .check_area .form-check label {
    text-transform: capitalize;
    font-size: 16px;
    font-weight: 400;
    color: var(--colorBlack);
    display: block;
    width: 100%;
}

.wsus__check_single_form .select2-container {
    width: 100% !important;
}

.wsus__check_single_form .select2-container--default .select2-selection--single {
    border: 1px solid #eee;
    border-radius: 3px;
    height: 48px;
}

.wsus__check_single_form .select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 48px;
    font-size: 16px;
    padding-left: 20px;
}

.wsus__check_single_form .select2-container--default .select2-selection--single .select2-selection__arrow {
    top: 11px;
    right: 15px;
}

.wsus__check_single_form .select2-container--default .select2-results__option--highlighted.select2-results__option--selectable {
    background: var(--colorPrimary) !important;
}

.preloader {
    width: 100%;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
    background: #ffffffc2;
    display: flex;
    justify-content: center;
    align-items: center;
}

.preloader .img {
    width: 60px;
    height: 60px;
}





.topbar_right {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: end;
}

.topbar_language {
    margin-right: 30px;
}

.topbar_language .nice-select {
    width: 120px;
    padding: 4px 20px;
    border-radius: 40px;
}

.topbar_language .nice-select:after {
    border-bottom: 1px solid #000;
    border-right: 1px solid #000;
    width: 6px;
    height: 6px;
    right: 20px;
}

.wsus__why_choose .wsus__choose_single:last-child {
    /* border: none; */
}
