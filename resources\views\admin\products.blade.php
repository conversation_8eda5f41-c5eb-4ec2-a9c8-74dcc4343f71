@extends('admin.admin-layout')

@section('content')
<div class="row">
    <!-- <PERSON> Header -->
    <div class="col-12">
        <div class="admin-page-header mb-4">
            <h2>Product Management</h2>
            <nav class="admin-breadcrumb">
                <span class="admin-breadcrumb-item">Dashboard</span>
                <span class="admin-breadcrumb-item active">Products</span>
            </nav>
        </div>
    </div>
</div>

<div class="row">
    <!-- Add Product Form -->
    <div class="col-xl-4 col-lg-5">
        <div class="admin-table-wrapper">
            <div class="admin-table-header">
                <h3 class="admin-table-title">Add New Product</h3>
            </div>
            <form>
                <div class="admin-form-group">
                    <label class="admin-form-label">Product Name</label>
                    <input type="text" class="admin-form-control" placeholder="Enter product name">
                </div>
                
                <div class="admin-form-group">
                    <label class="admin-form-label">Category</label>
                    <select class="admin-form-control">
                        <option>Select Category</option>
                        <option>Burger</option>
                        <option>Pizza</option>
                        <option>Chicken</option>
                        <option>Desserts</option>
                    </select>
                </div>
                
                <div class="admin-form-group">
                    <label class="admin-form-label">Price</label>
                    <input type="number" class="admin-form-control" placeholder="0.00" step="0.01">
                </div>
                
                <div class="admin-form-group">
                    <label class="admin-form-label">Description</label>
                    <textarea class="admin-form-control" rows="4" placeholder="Enter product description"></textarea>
                </div>
                
                <div class="admin-form-group">
                    <label class="admin-form-label">Product Image</label>
                    <input type="file" class="admin-form-control" accept="image/*">
                </div>
                
                <div class="admin-form-group">
                    <button type="submit" class="admin-btn w-100">
                        <i class="fas fa-plus-circle"></i> Add Product
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Products List -->
    <div class="col-xl-8 col-lg-7">
        <div class="admin-table-wrapper">
            <div class="admin-table-header">
                <h3 class="admin-table-title">All Products</h3>
                <div class="d-flex gap-2">
                    <input type="text" class="admin-form-control" placeholder="Search products..." style="width: 200px;">
                    <button class="admin-btn admin-btn-secondary">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
            
            <table class="admin-table">
                <thead>
                    <tr>
                        <th>Image</th>
                        <th>Name</th>
                        <th>Category</th>
                        <th>Price</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <img src="public/uploads/custom-images/hyderabadi-biryani-2023-03-05-01-14-59-9689.png" 
                                 alt="Product" style="width: 50px; height: 50px; border-radius: 8px; object-fit: cover;">
                        </td>
                        <td>Hyderabadi Biryani</td>
                        <td>Burger</td>
                        <td>$130.00</td>
                        <td>
                            <span class="admin-btn admin-btn-success admin-btn-sm">Active</span>
                        </td>
                        <td>
                            <div class="d-flex gap-1">
                                <button class="admin-btn admin-btn-secondary admin-btn-sm">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="admin-btn admin-btn-danger admin-btn-sm">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <img src="public/uploads/custom-images/spicy-burger-2023-03-05-02-57-08-4535.png" 
                                 alt="Product" style="width: 50px; height: 50px; border-radius: 8px; object-fit: cover;">
                        </td>
                        <td>Spicy Burger</td>
                        <td>Burger</td>
                        <td>$40.00</td>
                        <td>
                            <span class="admin-btn admin-btn-success admin-btn-sm">Active</span>
                        </td>
                        <td>
                            <div class="d-flex gap-1">
                                <button class="admin-btn admin-btn-secondary admin-btn-sm">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="admin-btn admin-btn-danger admin-btn-sm">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <img src="public/uploads/custom-images/fried-chicken-2023-03-05-02-59-51-6567.png" 
                                 alt="Product" style="width: 50px; height: 50px; border-radius: 8px; object-fit: cover;">
                        </td>
                        <td>Fried Chicken</td>
                        <td>Chicken</td>
                        <td>$50.00</td>
                        <td>
                            <span class="admin-btn admin-btn-sm" style="background: #6c757d; color: white;">Inactive</span>
                        </td>
                        <td>
                            <div class="d-flex gap-1">
                                <button class="admin-btn admin-btn-secondary admin-btn-sm">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="admin-btn admin-btn-danger admin-btn-sm">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <span style="color: var(--paraColor); font-size: 14px;">
                    Showing 1 to 3 of 89 entries
                </span>
                <div class="d-flex gap-1">
                    <button class="admin-btn admin-btn-secondary admin-btn-sm">Previous</button>
                    <button class="admin-btn admin-btn-sm">1</button>
                    <button class="admin-btn admin-btn-secondary admin-btn-sm">2</button>
                    <button class="admin-btn admin-btn-secondary admin-btn-sm">3</button>
                    <button class="admin-btn admin-btn-secondary admin-btn-sm">Next</button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Add some interactive functionality
    $('.admin-stat-card').hover(function() {
        $(this).addClass('admin-card-hover');
    }, function() {
        $(this).removeClass('admin-card-hover');
    });
});
</script>
@endpush
