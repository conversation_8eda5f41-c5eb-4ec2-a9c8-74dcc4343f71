@extends('admin.admin-layout')

@section('content')
<div class="row">
    <!-- Creative Header -->
    <div class="col-12">
        <div class="admin-creative-header mb-4">
            <div class="admin-header-content">
                <div class="admin-header-main">
                    <div class="admin-header-icon">
                        <i class="fas fa-plus-circle"></i>
                    </div>
                    <div class="admin-header-text">
                        <h1 class="admin-header-title">Create Hero Slider</h1>
                        <p class="admin-header-subtitle">Add a new slider to your homepage hero section</p>
                    </div>
                </div>
                <div class="admin-header-actions">
                    <a href="{{ route('admin.hero-sliders.index') }}" class="admin-btn admin-btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="admin-form-wrapper">
            <div class="admin-form-header">
                <h3 class="admin-form-title">Slider Information</h3>
            </div>
            
            <form action="{{ route('admin.hero-sliders.store') }}" method="POST" enctype="multipart/form-data">
                @csrf
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="admin-form-group">
                            <label for="title" class="admin-form-label">Title *</label>
                            <input type="text" class="admin-form-control @error('title') is-invalid @enderror" 
                                   id="title" name="title" value="{{ old('title') }}" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="admin-form-group">
                            <label for="subtitle" class="admin-form-label">Subtitle *</label>
                            <input type="text" class="admin-form-control @error('subtitle') is-invalid @enderror" 
                                   id="subtitle" name="subtitle" value="{{ old('subtitle') }}" required>
                            @error('subtitle')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
                
                <div class="admin-form-group">
                    <label for="description" class="admin-form-label">Description *</label>
                    <textarea class="admin-form-control @error('description') is-invalid @enderror" 
                              id="description" name="description" rows="4" required>{{ old('description') }}</textarea>
                    @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="admin-form-group">
                            <label for="button_text" class="admin-form-label">Button Text *</label>
                            <input type="text" class="admin-form-control @error('button_text') is-invalid @enderror" 
                                   id="button_text" name="button_text" value="{{ old('button_text', 'Shop now') }}" required>
                            @error('button_text')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="admin-form-group">
                            <label for="button_url" class="admin-form-label">Button URL</label>
                            <input type="text" class="admin-form-control @error('button_url') is-invalid @enderror" 
                                   id="button_url" name="button_url" value="{{ old('button_url') }}">
                            @error('button_url')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="admin-form-group">
                            <label for="offer_text" class="admin-form-label">Offer Text</label>
                            <input type="text" class="admin-form-control @error('offer_text') is-invalid @enderror" 
                                   id="offer_text" name="offer_text" value="{{ old('offer_text') }}" 
                                   placeholder="e.g., 35% off">
                            @error('offer_text')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="admin-form-group">
                            <label for="sort_order" class="admin-form-label">Sort Order</label>
                            <input type="number" class="admin-form-control @error('sort_order') is-invalid @enderror" 
                                   id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" min="0">
                            @error('sort_order')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
                
                <div class="admin-form-group">
                    <label for="image" class="admin-form-label">Slider Image *</label>
                    <div class="admin-file-upload">
                        <input type="file" class="admin-file-input @error('image') is-invalid @enderror" 
                               id="image" name="image" accept="image/*" required>
                        <label for="image" class="admin-file-label">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <span>Choose slider image</span>
                        </label>
                        <div class="admin-file-preview" id="imagePreview"></div>
                    </div>
                    @error('image')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="admin-form-actions">
                    <button type="submit" class="admin-btn admin-btn-primary">
                        <i class="fas fa-save"></i> Create Slider
                    </button>
                    <a href="{{ route('admin.hero-sliders.index') }}" class="admin-btn admin-btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="admin-info-card">
            <div class="admin-info-header">
                <h4><i class="fas fa-info-circle"></i> Slider Guidelines</h4>
            </div>
            <div class="admin-info-content">
                <ul class="admin-info-list">
                    <li><strong>Image Size:</strong> Recommended 1920x800px</li>
                    <li><strong>File Format:</strong> JPG, PNG, GIF</li>
                    <li><strong>Max File Size:</strong> 2MB</li>
                    <li><strong>Title:</strong> Keep it short and catchy</li>
                    <li><strong>Description:</strong> Brief description of the offer</li>
                    <li><strong>Sort Order:</strong> Lower numbers appear first</li>
                </ul>
            </div>
        </div>
        
        <div class="admin-status-card">
            <div class="admin-status-header">
                <h4><i class="fas fa-toggle-on"></i> Status Settings</h4>
            </div>
            <div class="admin-status-content">
                <div class="admin-form-group">
                    <div class="admin-checkbox-wrapper">
                        <input type="checkbox" id="is_active" name="is_active" value="1" 
                               {{ old('is_active', true) ? 'checked' : '' }}>
                        <label for="is_active" class="admin-checkbox-label">
                            <span class="admin-checkbox-text">Active</span>
                            <small class="admin-checkbox-help">Enable this slider to show on homepage</small>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const preview = document.getElementById('imagePreview');
    
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.innerHTML = `<img src="${e.target.result}" alt="Preview" class="img-fluid">`;
        };
        reader.readAsDataURL(file);
    } else {
        preview.innerHTML = '';
    }
});
</script>
@endsection
