@extends('admin.admin-layout')

@section('content')
<div class="row">
    <!-- Creative Header -->
    <div class="col-12">
        <div class="admin-creative-header mb-4">
            <div class="admin-header-content">
                <div class="admin-header-main">
                    <div class="admin-header-icon">
                        <i class="fas fa-quote-right"></i>
                    </div>
                    <div class="admin-header-text">
                        <h1 class="admin-header-title">Customer Testimonials</h1>
                        <p class="admin-header-subtitle">Manage customer reviews and feedback</p>
                    </div>
                </div>
                <div class="admin-header-actions">
                    <a href="{{ route('admin.testimonials.create') }}" class="admin-btn">
                        <i class="fas fa-plus"></i> Add New Testimonial
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="admin-table-wrapper">
            <div class="admin-table-header">
                <h3 class="admin-table-title">All Testimonials</h3>
                <div class="admin-table-actions">
                    <span class="admin-badge admin-badge-info">{{ $testimonials->count() }} Total</span>
                </div>
            </div>
            
            @if($testimonials->count() > 0)
                <div class="row">
                    @foreach($testimonials as $testimonial)
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="admin-testimonial-card">
                            <div class="admin-testimonial-header">
                                <div class="admin-testimonial-avatar">
                                    <img src="{{ asset('storage/' . $testimonial->image) }}" alt="{{ $testimonial->name }}">
                                </div>
                                <div class="admin-testimonial-status">
                                    <div class="admin-toggle-switch">
                                        <input type="checkbox" id="status-{{ $testimonial->id }}" 
                                               {{ $testimonial->is_active ? 'checked' : '' }}
                                               onchange="toggleStatus({{ $testimonial->id }}, '{{ route('admin.testimonials.toggle-status', $testimonial) }}')">
                                        <label for="status-{{ $testimonial->id }}"></label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="admin-testimonial-content">
                                <div class="admin-testimonial-rating">
                                    @for($i = 1; $i <= 5; $i++)
                                        @if($i <= $testimonial->rating)
                                            <i class="fas fa-star"></i>
                                        @else
                                            <i class="far fa-star"></i>
                                        @endif
                                    @endfor
                                    <span class="rating-text">({{ $testimonial->rating }}/5)</span>
                                </div>
                                
                                <div class="admin-testimonial-feedback">
                                    <i class="fas fa-quote-left quote-icon"></i>
                                    <p>{{ Str::limit($testimonial->feedback, 120) }}</p>
                                </div>
                                
                                <div class="admin-testimonial-author">
                                    <h5>{{ $testimonial->name }}</h5>
                                    <span>{{ $testimonial->position }}</span>
                                </div>
                                
                                <div class="admin-testimonial-meta">
                                    <span class="admin-badge admin-badge-secondary">Order: {{ $testimonial->sort_order }}</span>
                                    @if($testimonial->product_image)
                                        <span class="admin-badge admin-badge-info">Has Product</span>
                                    @endif
                                </div>
                                
                                <div class="admin-testimonial-actions">
                                    <a href="{{ route('admin.testimonials.show', $testimonial) }}" class="admin-btn admin-btn-sm admin-btn-info">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    <a href="{{ route('admin.testimonials.edit', $testimonial) }}" class="admin-btn admin-btn-sm admin-btn-warning">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    <form action="{{ route('admin.testimonials.destroy', $testimonial) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="admin-btn admin-btn-sm admin-btn-danger" 
                                                onclick="return confirm('Are you sure you want to delete this testimonial?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            @else
                <div class="admin-empty-state">
                    <div class="admin-empty-icon">
                        <i class="fas fa-quote-right"></i>
                    </div>
                    <h3>No Testimonials Found</h3>
                    <p>Add your first customer testimonial to build trust and credibility.</p>
                    <a href="{{ route('admin.testimonials.create') }}" class="admin-btn">
                        <i class="fas fa-plus"></i> Add First Testimonial
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<style>
.admin-testimonial-card {
    background: var(--cardBg);
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    height: 100%;
}

.admin-testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.admin-testimonial-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
}

.admin-testimonial-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid var(--primaryColor);
}

.admin-testimonial-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.admin-testimonial-status {
    position: absolute;
    top: 15px;
    right: 15px;
}

.admin-testimonial-rating {
    text-align: center;
    margin-bottom: 15px;
}

.admin-testimonial-rating i {
    color: #ffc107;
    font-size: 16px;
    margin-right: 2px;
}

.rating-text {
    font-size: 12px;
    color: var(--paraColor);
    margin-left: 8px;
}

.admin-testimonial-feedback {
    position: relative;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid var(--primaryColor);
}

.quote-icon {
    position: absolute;
    top: -5px;
    left: 10px;
    color: var(--primaryColor);
    font-size: 20px;
    background: var(--cardBg);
    padding: 5px;
}

.admin-testimonial-feedback p {
    margin: 0;
    font-style: italic;
    color: var(--paraColor);
    line-height: 1.5;
    padding-left: 20px;
}

.admin-testimonial-author {
    text-align: center;
    margin-bottom: 15px;
}

.admin-testimonial-author h5 {
    margin: 0 0 5px 0;
    color: var(--headingColor);
    font-weight: 600;
}

.admin-testimonial-author span {
    color: var(--primaryColor);
    font-size: 14px;
}

.admin-testimonial-meta {
    margin-bottom: 20px;
    text-align: center;
}

.admin-testimonial-meta .admin-badge {
    margin-right: 5px;
    margin-bottom: 5px;
}

.admin-testimonial-actions {
    display: flex;
    gap: 8px;
}

.admin-testimonial-actions .admin-btn {
    flex: 1;
    text-align: center;
}
</style>

<script>
function toggleStatus(id, url) {
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}
</script>
@endsection
