<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Counter;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class CounterController extends Controller
{
    public function index()
    {
        $counters = Counter::ordered()->get();
        return view('admin.content.counters.index', compact('counters'));
    }

    public function create()
    {
        return view('admin.content.counters.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'count' => 'required|integer|min:0',
            'icon' => 'required|string|max:255',
            'background_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0'
        ]);

        $data = $request->all();
        
        if ($request->hasFile('background_image')) {
            $data['background_image'] = $request->file('background_image')->store('counters', 'public');
        }

        Counter::create($data);

        return redirect()->route('admin.counters.index')
            ->with('success', 'Counter created successfully.');
    }

    public function show(Counter $counter)
    {
        return view('admin.content.counters.show', compact('counter'));
    }

    public function edit(Counter $counter)
    {
        return view('admin.content.counters.edit', compact('counter'));
    }

    public function update(Request $request, Counter $counter)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'count' => 'required|integer|min:0',
            'icon' => 'required|string|max:255',
            'background_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0'
        ]);

        $data = $request->all();
        
        if ($request->hasFile('background_image')) {
            // Delete old image
            if ($counter->background_image && Storage::disk('public')->exists($counter->background_image)) {
                Storage::disk('public')->delete($counter->background_image);
            }
            $data['background_image'] = $request->file('background_image')->store('counters', 'public');
        }

        $counter->update($data);

        return redirect()->route('admin.counters.index')
            ->with('success', 'Counter updated successfully.');
    }

    public function destroy(Counter $counter)
    {
        // Delete image
        if ($counter->background_image && Storage::disk('public')->exists($counter->background_image)) {
            Storage::disk('public')->delete($counter->background_image);
        }

        $counter->delete();

        return redirect()->route('admin.counters.index')
            ->with('success', 'Counter deleted successfully.');
    }

    public function toggleStatus(Counter $counter)
    {
        $counter->update(['is_active' => !$counter->is_active]);
        
        return response()->json([
            'success' => true,
            'message' => 'Status updated successfully.',
            'is_active' => $counter->is_active
        ]);
    }
}
