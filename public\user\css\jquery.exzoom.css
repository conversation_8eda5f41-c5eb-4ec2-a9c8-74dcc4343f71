.exzoom {
  box-sizing: border-box;
}

.exzoom * {
  box-sizing: border-box;
}

.exzoom .exzoom_img_box {
  /* background: #eee; */
  position: relative;
}

.exzoom .exzoom_img_box .exzoom_main_img {
  display: block;
  width: 100%;
}

.exzoom .exzoom_preview {
  margin: 0;
  position: absolute;
  top: 0;
  overflow: hidden;
  z-index: 999;
  background-color: #fff;
  border: 1px solid #ddd;
  display: none;
}

.exzoom .exzoom_preview .exzoom_preview_img {
  position: relative;
  max-width: initial !important;
  max-height: initial !important;
  left: 0;
  top: 0;
}

.exzoom .exzoom_nav {
  margin-top: 10px;
  overflow: hidden;
  position: relative;
  left: 15px;
}

.exzoom .exzoom_nav .exzoom_nav_inner {
  position: absolute;
  left: 0;
  top: 0;
  margin: 0;
}

.exzoom .exzoom_nav .exzoom_nav_inner span {
  border: 1px solid var(--colorPrimary);
  overflow: hidden;
  position: relative;
  float: left;
  margin: 0px 2px !important;
  transition: all linear .3s;
  -webkit-transition: all linear .3s ease;
  -moz-transition: all linear .3s ease;
  -ms-transition: all linear .3s ease;
  -o-transition: all linear .3s ease;
  width: 60px !important;
  height: 60px !important;
}

.exzoom .exzoom_nav .exzoom_nav_inner span.current {
  border: 4px solid var(--colorPrimary);
}

.exzoom .exzoom_nav .exzoom_nav_inner span img {
  width: 100%;
  height: 100%;
  position: relative;
  top: 0 !important;
  object-fit: cover;
  left: 0 !important;
}

.exzoom .exzoom_btn {
  position: relative;
  margin: 0;
}

.exzoom .exzoom_btn a {
  display: block;
  width: 15px;
  height: 60px;
  line-height: 60px;
  background: var(--gradiantBg);
  text-align: center;
  font-size: 13px;
  position: absolute;
  left: 0;
  top: -62px;
  text-decoration: none;
  color: #fff;
  font-weight: 600;
  transition: all linear .3s;
  -webkit-transition: all linear .3s ease;
  -moz-transition: all linear .3s ease;
  -ms-transition: all linear .3s ease;
  -o-transition: all linear .3s ease;
}

.exzoom .exzoom_btn a:hover {
  background: var(--gradiantHoverBg);
  color: #fff;
}

.exzoom .exzoom_btn a.exzoom_next_btn {
  left: auto;
  right: 0;
}

.exzoom .exzoom_zoom {
  position: absolute;
  left: 0;
  top: 0;
  display: none;
  z-index: 5;
  cursor: pointer;
}

.exzoom .exzoom_img_ul_outer {
  /* border: 1px solid #ddd; */
  position: absolute;
  overflow: hidden;
  /* padding: 10px; */
}

.exzoom .exzoom_img_ul_outer .exzoom_img_ul {
  padding: 0;
  margin: 0;
  overflow: hidden;
  position: absolute;
  height: 100%;
}

.exzoom .exzoom_img_ul_outer .exzoom_img_ul li {
  list-style: none;
  display: inline-block;
  text-align: center;
  height: 100%;
}

.exzoom .exzoom_img_ul_outer .exzoom_img_ul li img {
  margin-top: 5% !important;
  width: 90% !important;
  height: 90% !important;
  object-fit: cover !important;
}

@media screen and (max-width: 768px) {
  .exzoom .exzoom_zoom_outer {
    display: none;
  }
}