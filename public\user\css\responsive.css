@media (min-width: 1200px) and (max-width: 1399.99px) {

    /* ======HOME PAGE START====== */
    .wsus__banner_text h1 {
        font-size: 50px;
    }

    .wsus__banner_img .img {
        height: 450px;
        width: 450px;
    }

    .wsus__single_blog_img {
        height: 450px;
    }

    .wsus__menu_item .wsus__menu_item_text .title {
        font-size: 21px;
    }

    /* ======HOME PAGE END====== */


    /* ======ABOUT PAGE START====== */
    .wsus__scroll_btn {
        right: -25px;
    }

    /* ======ABOUT PAGE END====== */

    .wsus__single_comm_text {
        width: 83%;
    }

    .wsus__single_comment.replay .wsus__single_comm_text {
        width: 82%;
    }

    .wsus__single_payment {
        height: 95px;
    }

    .wsus__download2 .wsus__download_img {
        height: 360px;
    }

    .wsus__subscribe_text h3 {
        font-size: 35px;
    }

    .latest_post li .text {
        width: 73%;
    }

    .details_extra_item .form-check label {
        max-width: 30%;
    }

    .wsus__offer_item_single {
        padding: 25px 100px 25px 25px;
    }

    .wsus__download_img {
        height: 400px;
    }

}



@media (min-width: 992px) and (max-width: 1199.99px) {

    /* ======HOME PAGE START====== */
    .main_menu .navbar-nav .nav-item .nav-link {
        margin: 0px 10px;
    }

    .wsus__banner_text h1 {
        font-size: 45px;
    }

    .wsus__banner_img .img {
        height: 380px;
        width: 380px;
        margin-bottom: 30px;
    }

    .wsus__banner_img span {
        width: 100px;
        height: 100px;
        top: -10px;
        right: 30px;
        font-size: 18px;
    }

    .banner_shape_1 {
        top: 10px;
        left: 0;
    }

    .wsus__choose_single {
        border: none;
    }

    .banner_shape_2 {
        top: 85%;
        left: 2%;
    }

    .banner_shape_3 {
        top: -60px;
        right: 5%;
    }

    .wsus__offer_item_single {
        padding: 30px 180px 30px 30px;
    }

    .wsus__menu .banner_shape_2 {
        width: 150px;
        height: 100px;
    }

    .wsus__menu .banner_shape_1 {
        width: 140px;
        height: 120px;
    }

    .wsus__team .banner_shape_1 {
        width: 160px;
        height: 130px;
    }

    .wsus__download_overlay {
        padding: 25px;
    }

    .wsus__download_img {
        height: 370px;
    }

    .wsus__single_counter .text h2 {
        font-size: 30px;
    }

    .wsus__single_counter .text p {
        font-size: 16px;
    }

    .wsus__single_counter i {
        font-size: 40px;
    }

    .wsus__section_heading p {
        padding: 0;
    }

    .wsus__single_blog_text {
        padding: 20px;
    }

    .wsus__single_blog_img {
        height: 400px;
    }

    .wsus__single_blog_text .title {
        font-size: 20px;
        margin-top: 10px;
    }

    .wsus__single_blog_text ul li {
        margin-bottom: 5px;
    }

    /* ======HOME PAGE END====== */


    /* ======ABOUT PAGE START====== */
    .wsus__about_us_img {
        margin-right: 0;
    }

    .wsus__about_choose_text {
        padding: 15px;
        margin-top: 10px;
    }

    .wsus__about_choose_img .img_2 {
        right: 0;
    }

    .wsus__scroll_btn {
        right: -25px;
    }

    /* ======ABOUT PAGE END====== */




    /* ======BLOG DETAILS START====== */
    .wsus__blog_details_text h1,
    .wsus__blog_details_text h2 {
        font-size: 30px;
    }

    .wsus__blog_det_slider_item {
        height: 200px;
    }

    .blog_det_button li {
        padding: 15px;
    }

    .blog_det_button li a p {
        font-size: 16px;
        width: 55%;
    }

    .wsus__single_comm_text {
        width: 79%;
    }

    .wsus__single_comment.replay .wsus__single_comm_text {
        width: 76%;
    }

    .wsus__related_blog ul li .text {
        width: 60%;
    }

    /* ======BLOG DETAILS END====== */


    /* ======CONTACT START====== */
    .wsus__contact_form {
        margin-bottom: 25px;
    }

    .wsus__contact_map {
        height: 450px;
    }

    .wsus__contact_info {
        padding: 25px;
    }

    /* ======CONTACT END====== */



    /* ======DASHBOARD START====== */
    .wsus__invoice_header .header_address {
        max-width: 45%;
    }

    /* ======DASHBOARD END====== */

    /* ======MENU DETAILS START====== */
    .wsus__menu_details_text h2 {
        font-size: 30px;
    }

    .wsus__menu_details_text .rating {
        margin: 12px 0px 17px 0px;
    }

    .wsus__about_choose_text ul li {
        font-size: 14px;
    }

    /* ======MENU DETAILS END====== */

    .wsus__single_payment {
        height: 80px;
    }

    .wsus__download2 .wsus__download_img {
        height: 380px;
    }

    .wsus__testimonial_product2 {
        width: 120px;
        height: 120px;
    }

    .wsus__testimonial_text2 {
        max-width: 63%;
        padding-left: 15px;
        margin-left: 15px;
    }

    .wsus__subscribe_text h3 {
        font-size: 28px;
    }

    .latest_post li .text {
        width: 68%;
    }

    .wsus__terms_condition h3 {
        font-size: 26px;
    }

    .details_extra_item .form-check label {
        max-width: 35%;
    }

}



@media (min-width: 768px) and (max-width: 991.99px) {

    /* ======HOME PAGE START====== */
    /* menu start */
    .main_menu .navbar-brand {
        width: 120px;
        margin-left: 12px;
    }

    .main_menu .navbar-toggler {
        width: 35px;
        height: 35px;
        padding: 0;
        line-height: 35px;
        text-align: center;
        border-radius: 2px;
        background: var(--gradiantBg);
        color: var(--colorWhite);
        border: none;
        margin-right: 12px;
    }

    .main_menu .navbar-toggler:hover {
        background: var(--gradiantHoverBg);
    }

    .main_menu .container {
        padding: 7px 0px 12px 0px;
    }

    .main_menu .navbar-nav {
        text-align: center;
        line-height: 50px;
    }

    .main_menu #navbarNav {
        background: var(--colorWhite);
        margin-top: 15px;
        padding-bottom: 20px;
        padding-top: 10px;
    }

    .main_menu .menu_icon {
        justify-content: center;
        margin-top: 10px;
    }

    .main_menu .menu_icon li:first-child a {
        margin-left: 0;
    }

    .droap_menu {
        z-index: 9;
        max-height: 200px;
        left: 50%;
        text-align: left;
        transform: translateX(-50%);
        -webkit-transform: translateX(-50%);
        -moz-transform: translateX(-50%);
        -ms-transform: translateX(-50%);
        -o-transform: translateX(-50%);
    }

    .wsus__search_form form {
        width: 75%;
    }

    .wsus__menu_cart_area ul li .menu_cart_img {
        width: 55px;
        height: 55px;
    }

    .wsus__menu_cart_area ul li .menu_cart_text {
        width: 80%;
    }

    .wsus__menu_cart_area ul {
        max-height: 340px;
    }

    /* menu end */


    /* banner start */
    .wsus__banner {
        padding-top: 130px;
    }

    .banner_shape_1 {
        width: 100px;
        height: 85px;
        top: 10px;
        left: 10px;
    }

    .wsus__banner_img span {
        top: 0;
        width: 90px;
        height: 90px;
        right: 0;
        font-size: 14px;
    }

    .wsus__banner_text h1 {
        font-size: 35px;
    }

    .wsus__banner_text h3 {
        font-size: 23px;
    }

    .wsus__banner_text p {
        font-size: 16px;
    }

    .common_btn {
        padding: 10px 55px 10px 20px !important;
        font-size: 14px !important;
    }

    .wsus__banner_text ul li a {
        margin: 0px 5px;
    }

    .banner_shape_2 {
        top: 85%;
        left: 10px;
    }

    /* banner end */


    /* why choose start */
    .wsus__choose_single {
        border: none;
    }

    .wsus__choose_single .text {
        max-width: 78%;
    }

    /* why choose end */


    /* daily offer start */
    .banner_shape_3 {
        width: 130px;
        height: 90px;
        top: -50px;
        right: 5%;
    }

    .wsus__section_heading h2 {
        font-size: 30px;
    }

    .wsus__section_heading p {
        padding: 0;
    }

    .wsus__offer_item_single {
        padding: 20px 100px 20px 20px;
    }

    /* daily offer end */


    /* menu start */
    .wsus__menu .banner_shape_2 {
        width: 90px;
        height: 65px;
        top: -30px;
        right: 5%;
    }

    .wsus__menu .banner_shape_1 {
        width: 90px;
        height: 75px;
        top: -45px;
    }

    /* menu end */


    /* special offer start */
    .wsus__special_offer_text h3 {
        font-size: 30px;
    }

    .wsus__special_offer_text h5 {
        font-size: 18px;
    }

    .wsus__team .banner_shape_1 {
        right: 10%;
        width: 65px;
        height: 55px;
    }

    .wsus__team .banner_shape_2 {
        top: 40px;
    }

    .wsus__single_team_text h4 {
        font-size: 20px;
    }

    /* special offer end */


    /* download app start */
    .wsus__download_overlay {
        padding: 40px;
    }

    .wsus__download_text h4,
    .wsus__download_text h2,
    .wsus__download_text span,
    .wsus__download_text p {
        text-align: center !important;
    }

    .wsus__download_text ul {
        justify-content: center;
    }

    .wsus__download_text ul li a {
        margin: 0px 10px;
    }

    .wsus__download_img {
        height: 530px;
        margin-top: 30px;
    }

    /* download app end */


    /* counter start */
    .wsus__single_counter {
        margin-bottom: 30px;
    }

    .wsus__counter_overlay {
        padding-bottom: 75px !important;
    }

    /* counter end */

    .wsus__testimonial_product {
        width: 80px;
        height: 80px;
    }

    /* blog start */
    .wsus__single_blog_img {
        height: 350px;
    }

    .wsus__single_blog_text .title {
        font-size: 18px;
        margin-top: 10px;
    }

    .wsus__single_blog_text .category {
        left: 20px;
    }

    .wsus__single_blog_text ul li {
        margin-bottom: 5px;
    }

    .wsus__single_blog_text {
        padding: 20px;
    }

    /* blog end */


    /* footer start */
    .wsus__footer_content {
        margin-bottom: 50px;
    }

    .wsus__footer_content h3 {
        margin-bottom: 5px;
    }

    .footer_overlay {
        padding-bottom: 45px !important;
    }

    .footer2 .footer_overlay {
        padding-bottom: 0 !important;
    }

    .wsus__footer_bottom_text p {
        text-align: center;
        width: 100%;
    }

    .wsus__footer_bottom_text ul {
        justify-content: center;
        margin-top: 20px;
        width: 100%;
    }

    .wsus__scroll_btn {
        bottom: 35px;
        right: -25px;
    }

    /* footer end */
    /* ======HOME PAGE END====== */


    /* ======ABOUT PAGE START====== */
    .wsus__breadcrumb_text h1 {
        font-size: 40px;
    }

    .wsus__breadcrumb_overlay {
        padding: 230px 0px 113px 0px;
    }

    .wsus__about_us_img {
        margin-bottom: 25px;
        margin-right: 0;
    }

    .wsus__about_video_text p {
        font-size: 30px;
        margin-right: 30px;
    }

    .wsus__about_choose_text {
        padding: 15px;
        margin-top: 10px;
    }

    /* ======ABOUT PAGE END====== */


    /* ======BLOG DETAILS START====== */
    .wsus__blog_details_img {
        height: 400px;
    }

    .wsus__blog_quot_text h4 {
        font-size: 16px;
    }

    .blog_det_button li a img {
        height: 110px !important;
    }

    .blog_det_button li a p {
        width: 56%;
    }

    .blog_det_button li {
        padding: 15px;
    }

    .blog_det_button li a p span {
        font-size: 16px;
    }

    .wsus__single_comm_text {
        width: 85%;
    }

    .wsus__single_comment.replay .wsus__single_comm_text {
        width: 84%;
    }

    .wsus__single_comm_text h3 {
        margin-top: 10px !important;
    }

    .wsus__single_comment.replay {
        padding-left: 30px;
    }

    .wsus__single_comment img {
        width: 80px !important;
        height: 80px !important;
    }

    .comment_input {
        margin-bottom: 25px;
    }

    .comment_input h4 {
        font-size: 20px;
    }

    .blog_sidebar h3 {
        font-size: 18px;
    }

    .wsus__related_blog ul li .text {
        width: 85%;
    }

    .wsus__blog_det_slider_item {
        height: 160px;
    }

    /* ======BLOG DETAILS END====== */

    .wsus__cart_list {
        margin-bottom: 25px;
    }

    .wsus__check_form {
        margin-bottom: 25px;
        padding: 20px 20px 0px 20px;
    }

    /* ======CONTACT START====== */
    .wsus__contact_form {
        margin-bottom: 25px;
    }

    .wsus__contact_map {
        height: 400px;
    }

    /* ======CONTACT END====== */


    /* ======DASHBOARD START====== */
    .wsus__dashboard_area {
        border: none;
    }

    .wsus__dashboard_content {
        padding: 0;
    }

    .wsus_dash_personal_info {
        border-radius: 0;
        margin: 0;
    }

    .wsus_dashboard_body h3 {
        font-size: 20px;
        margin-top: 25px;
    }

    .wsus_dash_personal_info h4 {
        font-size: 16px;
    }

    .wsus_dash_personal_info p {
        flex-wrap: wrap;
    }

    .wsus_dash_personal_info.show .wsus_dash_personal_info_edit {
        margin: 0;
    }

    .wsus_dashboard_order table {
        border-radius: 0;
    }

    .wsus_dashboard_order table tr th,
    .wsus_dashboard_order table tr td {
        width: 139px;
    }

    .wsus_dashboard_body .wsus__invoice {
        border-radius: 0px;
        padding: 15px;
        -webkit-border-radius: 0px;
        -moz-border-radius: 0px;
        -ms-border-radius: 0px;
        -o-border-radius: 0px;
    }

    .wsus__invoice_header .header_address {
        max-width: 40%;
    }

    .wsus__invoice_header {
        margin-top: 15px;
    }

    .wsus__invoice .sl_no {
        min-width: 80px;
    }

    .wsus__invoice .package {
        min-width: 180px;
    }

    .wsus__invoice .price,
    .wsus__invoice .qnty,
    .wsus__invoice .total {
        min-width: 100px;
    }

    .wsus__invoice_header .header_address h4 {
        font-size: 18px;
    }

    .wsus__single_comm_text .rating {
        margin-top: 10px;
    }

    /* ======DASHBOARD END====== */

    /* ======MENU DETAILS START====== */
    .wsus__menu_details_images {
        height: 516px;
    }

    .wsus__menu_details_text {
        margin-top: 25px;
    }

    .wsus__menu_details_text h2 {
        font-size: 30px;
    }

    .wsus__menu_details_text .rating {
        margin: 12px 0px 15px 0px;
    }

    .wsus__menu_details_text .price {
        font-size: 20px;
        margin-bottom: 10px;
    }

    .wsus__menu_description_area .nav .nav-item .nav-link {
        font-size: 16px;
    }

    .menu_det_description ul li {
        width: 100%;
    }

    .wsus__post_review {
        margin-top: 25px;
    }

    .menu_det_adition_info ul li {
        width: 48% !important;
    }

    .menu_det_description ul li::after {
        top: 7px;
    }

    .wsus__related_menu h2 {
        font-size: 22px;
    }

    /* ======MENU DETAILS END====== */

    .wsus__single_payment {
        height: 80px;
    }

    /* ======404 start====== */

    .wsus__404_text h2 {
        font-size: 30px;
    }

    .wsus__404_text img {
        height: 220px !important;
        width: 360px !important;
    }

    /* ======404 END====== */

    .wsus__terms_condition h3 {
        font-size: 22px;
    }

    /* ======HOME 2 START====== */
    .wsus__banner_text2 {
        padding-bottom: 0 !important;
    }

    .home_2 .main_menu .navbar-nav .nav-item .nav-link {
        color: var(--colorBlack);
    }

    .wsus__menu2 .menu_filter button {
        padding: 5px 20px;
    }

    .wsus__single_team_text2 h4 {
        font-size: 20px;
    }

    .wsus__download2 .wsus__download_img {
        height: 380px !important;
        margin-bottom: 25px;
    }

    .wsus__single_testimonial2 {
        padding: 25px;
    }

    .wsus__testimonial_text2 h4 {
        font-size: 16px;
    }

    .latest_post li .text {
        width: 85% !important;
    }

    .wsus__testimonial2 .nextArrow,
    .wsus__testimonial2 .prevArrow {
        right: 0px;
    }

    .wsus__testimonial2 .prevArrow {
        left: 0;
    }

    .wsus__subscribe_text h3 {
        font-size: 25px;
    }

    .subscribe_form {
        margin-top: 15px;
    }

    .latest_post li .text {
        width: 68%;
    }

    .footer2 .wsus__footer_bottom {
        margin-top: 50px !important;
    }

    .wsus__footer_social_link {
        width: 100%;
    }

    .wsus__blog2 .wsus__blog_overlay {
        padding: 65px 0px 65px 0px !important;
    }

    .wsus__menu2 .wsus__menu_item img {
        height: 250px !important;
    }

    .wsus__offer_item2 .wsus__offer_item_single .img {
        width: 110px;
        height: 110px;
    }

    .wsus__banner2 .prevArrow,
    .wsus__banner2 .nextArrow {
        display: none !important;
    }

    /* ======HOME 2 END====== */

    .details_extra_item .form-check label {
        max-width: 30%;
    }

    .wsus__banner_img .img {
        margin-bottom: 30px;
        height: 280px;
        width: 280px;
    }

    .wsus_dashboard_existing_address {
        margin-bottom: -20px;
    }

    .wsus_dashboard_new_address h4,
    .wsus_dashboard_edit_address h4 {
        font-size: 16px;
    }

    .wsus__faq_form {
        margin-top: 25px;
    }

    .exzoom .exzoom_preview {
        display: none !important;
    }

    .wsus__topbar_info li:last-child {
        display: none;
    }
}


@media (min-width: 576px) and (max-width: 767.99px) {

    /* ======HOME PAGE START====== */
    /* topbar start */
    .wsus__topbar {
        position: relative;
    }

    .wsus__topbar_info {
        justify-content: center;
    }

    .wsus__topbar_info li {
        margin: 0px 15px !important;
    }

    .wsus__topbar_info li a {
        font-size: 13px;
    }

    .wsus__topbar_info li a i {
        width: auto;
        height: auto;
        line-height: inherit;
        font-size: 13px;
        margin-right: 5px;
        background: none !important;
        color: var(--colorWhite) !important;
    }

    .topbar_icon {
        justify-content: center;
    }

    .topbar_icon li a {
        margin: 0px 5px;
    }

    /* topbar end */


    /* menu start */
    .main_menu .navbar-brand {
        width: 120px;
        margin-left: 12px;
    }

    .main_menu .navbar-toggler {
        width: 35px;
        height: 35px;
        padding: 0;
        line-height: 35px;
        text-align: center;
        border-radius: 2px;
        background: var(--gradiantBg);
        color: var(--colorWhite);
        border: none;
        margin-right: 12px;
    }

    .main_menu .navbar-toggler:hover {
        background: var(--gradiantHoverBg);
    }

    .main_menu .container {
        padding: 7px 0px 12px 0px;
    }

    .main_menu .navbar-nav {
        text-align: center;
        line-height: 50px;
    }

    .main_menu #navbarNav {
        background: var(--colorWhite);
        margin-top: 15px;
        padding-bottom: 20px;
        padding-top: 10px;
    }

    .main_menu .menu_icon {
        justify-content: center;
        margin-top: 10px;
    }

    .main_menu .menu_icon li:first-child a {
        margin-left: 0;
    }

    .droap_menu {
        z-index: 9;
        max-height: 200px;
        left: 50%;
        text-align: left;
        transform: translateX(-50%);
        -webkit-transform: translateX(-50%);
        -moz-transform: translateX(-50%);
        -ms-transform: translateX(-50%);
        -o-transform: translateX(-50%);
    }

    .wsus__search_form form {
        width: 80%;
    }

    .wsus__menu_cart_area ul li .menu_cart_img {
        width: 55px;
        height: 55px;
    }

    .wsus__menu_cart_area ul li .menu_cart_text {
        width: 80%;
    }

    .wsus__menu_cart_area ul {
        max-height: 340px;
    }

    /* menu end */


    /* banner start */

    .wsus__banner {
        padding-top: 80px;
        height: auto;
    }

    .banner_shape_1 {
        width: 100px;
        height: 85px;
        top: 10px;
        left: 10px;
    }

    .wsus__banner_img {
        margin: 65px 0px 20px 0px;
    }

    .wsus__banner_img span {
        top: -20px;
        right: -20px;
    }

    .wsus__banner_text h1 {
        font-size: 35px;
        text-align: center;
    }

    .wsus__banner_text h3 {
        font-size: 23px;
        text-align: center;
    }

    .wsus__banner_text p {
        font-size: 16px;
        text-align: center;
    }

    .common_btn {
        padding: 10px 55px 10px 20px !important;
        font-size: 14px !important;
    }

    .wsus__banner_text ul {
        justify-content: center;
    }

    .wsus__banner_text ul li a {
        margin: 0px 5px;
    }

    .wsus__banner_overlay div {
        height: auto;
    }

    .wsus__banner_text {
        padding-bottom: 100px;
    }

    .banner_shape_2 {
        top: 90%;
        left: 10px;
    }

    /* banner end */


    /* why choose start */
    .wsus__choose_single {
        border: none;
    }

    .wsus__choose_single .text {
        max-width: 84%;
    }

    /* why choose end */


    /* daily offer start */
    .banner_shape_3 {
        width: 130px;
        height: 90px;
        top: -50px;
        right: 5%;
    }

    .wsus__section_heading h2 {
        font-size: 30px;
    }

    .wsus__section_heading p {
        padding: 0px 30px;
    }

    .wsus__offer_item_single {
        padding: 30px 215px 30px 30px;
    }

    /* daily offer end */


    /* menu start */
    .wsus__menu .banner_shape_2 {
        width: 90px;
        height: 65px;
        top: -30px;
        right: 5%;
    }

    .wsus__menu .banner_shape_1 {
        width: 90px;
        height: 75px;
        top: -45px;
    }

    .menu_filter button {
        padding: 5px 20px;
    }

    .wsus__menu_item .wsus__menu_item_text {
        padding: 165px 15px 15px 15px;
    }

    .wsus__menu_item .wsus__menu_item_text .title {
        font-size: 20px;
    }

    /* menu end */


    /* special offer start */
    .wsus__special_offer_text h3 {
        font-size: 30px;
    }

    .wsus__special_offer_text h5 {
        font-size: 18px;
    }

    .wsus__team .banner_shape_1 {
        right: 10%;
        width: 65px;
        height: 55px;
    }

    .wsus__team .banner_shape_2 {
        top: 40px;
    }

    .wsus__single_team_text h4 {
        font-size: 20px;
    }

    /* special offer end */

    .wsus__single_team {
        padding: 175px 15px 15px 15px;
    }

    /* download app start */
    .wsus__download_overlay {
        padding: 20px;
    }

    .wsus__download_text h4,
    .wsus__download_text h2,
    .wsus__download_text span,
    .wsus__download_text p {
        text-align: center !important;
    }

    .wsus__download_text ul {
        justify-content: center;
    }

    .wsus__download_text ul li a {
        margin: 0px 10px;
    }

    .wsus__download_img {
        height: 410px;
        margin-top: 30px;
    }

    /* download app end */


    /* counter start */
    .wsus__single_counter {
        margin-bottom: 30px;
    }

    .wsus__single_counter i {
        font-size: 40px;
    }

    .wsus__single_counter .text h2 {
        font-size: 30px;
    }

    .wsus__single_counter .text p {
        font-size: 15px;
    }

    .wsus__single_counter .text h2::after {
        font-size: 30px;
        right: -20px;
    }

    .wsus__counter_overlay {
        padding-bottom: 45px !important;
    }

    /* counter end */


    /* blog start */
    .wsus__single_blog_img {
        height: 350px;
    }

    .wsus__single_blog_text .title {
        font-size: 18px;
        margin-top: 10px;
    }

    .wsus__single_blog_text .category {
        left: 20px;
    }

    .wsus__single_blog_text ul li {
        margin-bottom: 5px;
    }

    .wsus__single_blog_text {
        padding: 20px;
    }

    /* blog end */


    /* footer start */
    .wsus__footer_content {
        margin-bottom: 50px;
    }

    .wsus__footer_content h3 {
        margin-bottom: 5px;
    }

    .footer_overlay {
        padding-bottom: 18px !important;
    }

    .footer2 .footer_overlay {
        padding-bottom: 0 !important;
    }

    .wsus__footer_bottom_text p {
        text-align: center;
        width: 100%;
    }

    .wsus__footer_bottom_text ul {
        justify-content: center;
        margin-top: 20px;
        width: 100%;
    }

    .wsus__scroll_btn {
        bottom: 35px;
        right: -25px;
    }

    /* footer end */
    /* ======HOME PAGE END====== */


    /* ======ABOUT PAGE START====== */
    .wsus__breadcrumb_text h1 {
        font-size: 30px;
    }

    .wsus__breadcrumb_overlay {
        padding: 155px 0px 87px 0px;
    }

    .wsus__about_us_img {
        height: 300px;
        margin-bottom: 25px;
        margin-right: 0;
    }

    .wsus__about_us_text ul li {
        width: 100%;
    }

    .wsus__about_video_text p {
        font-size: 30px;
        margin-right: 30px;
    }

    .wsus__about_video_overlay {
        padding: 80px 0px;
    }

    .wsus__about_choose_text {
        padding: 15px;
        margin-top: 10px;
    }

    .wsus__about_choose_text ul li {
        width: 100%;
    }

    /* ======ABOUT PAGE END====== */


    /* ======BLOG DETAILS START====== */
    .wsus__blog_details_img {
        height: 300px;
    }

    .wsus__blog_details_text h1,
    .wsus__blog_details_text h2 {
        font-size: 25px;
    }

    .wsus__blog_quot_text {
        padding: 20px 20px 20px 40px;
    }

    .wsus__blog_quot_text .left_icon {
        left: 5px;
        top: 10px;
        width: 25px;
    }

    .wsus__blog_quot_text p span {
        margin-left: 10px;
        width: 25px;
    }

    .wsus__blog_quot_text h4 {
        font-size: 16px;
    }

    .wsus__blog_details_text h3 {
        font-size: 20px;
    }

    .blog_tags_share .tags {
        margin-bottom: 15px;
    }

    .blog_det_button li {
        width: 100%;
    }

    .blog_det_button li a img {
        height: 110px !important;
    }

    .blog_det_button li a p {
        width: 70%;
    }

    .blog_det_button li:first-child {
        margin-bottom: 25px;
    }

    .blog_det_button li a p span {
        font-size: 16px;
    }

    .wsus__single_comm_text {
        width: 80%;
    }

    .wsus__single_comment.replay .wsus__single_comm_text {
        width: 78%;
    }

    .wsus__single_comm_text h3 {
        margin-top: 10px !important;
    }

    .wsus__single_comment.replay {
        padding-left: 30px;
    }

    .wsus__single_comment img {
        width: 80px !important;
        height: 80px !important;
    }

    .comment_input {
        margin-bottom: 25px;
    }

    .comment_input h4 {
        font-size: 20px;
    }


    .blog_sidebar h3 {
        font-size: 18px;
    }

    .wsus__related_blog ul li .text {
        width: 80%;
    }

    /* ======BLOG DETAILS END====== */


    .wsus__cart_list {
        margin-bottom: 25px;
    }

    .wsus__check_form {
        margin-bottom: 25px;
        padding: 20px 20px 0px 20px;
    }

    .wsus__pagination ul li a {
        width: 40px !important;
        height: 40px !important;
        line-height: 40px !important;
    }


    /* ======CONTACT START====== */
    .wsus__contact_form {
        margin-bottom: 25px;
    }

    .wsus__contact_map {
        height: 300px;
    }

    /* ======CONTACT END====== */


    /* ======DASHBOARD START====== */
    .wsus__dashboard_area {
        border: none;
    }

    .wsus__dashboard_content {
        padding: 0 !important;
    }

    .wsus_dash_personal_info {
        border-radius: 0;
        margin: 0;
    }

    .wsus_dashboard_body h3 {
        font-size: 20px;
        margin-top: 25px;
    }

    .wsus_dash_personal_info h4 {
        font-size: 16px;
    }

    .wsus_dash_personal_info.show .wsus_dash_personal_info_edit {
        margin: 0;
    }

    .wsus_dashboard_order table {
        border-radius: 0;
    }

    .wsus_dashboard_order table tr th,
    .wsus_dashboard_order table tr td {
        width: 140px;
    }

    .wsus_dashboard_body .wsus__invoice {
        border-radius: 0px;
        padding: 15px;
        -webkit-border-radius: 0px;
        -moz-border-radius: 0px;
        -ms-border-radius: 0px;
        -o-border-radius: 0px;
    }

    .wsus__invoice_header .header_address {
        max-width: 40%;
    }

    .wsus__invoice_header {
        margin-top: 15px;
    }

    .wsus__invoice .sl_no {
        min-width: 80px;
    }

    .wsus__invoice .package {
        min-width: 180px;
    }

    .wsus__invoice .price,
    .wsus__invoice .qnty,
    .wsus__invoice .total {
        min-width: 100px;
    }

    .wsus__invoice_header .header_address h4 {
        font-size: 18px;
    }

    .wsus__single_comm_text .rating {
        margin-top: 10px;
    }

    /* ======DASHBOARD END====== */

    /* ======LOGIN START====== */
    .wsus__login_area {
        padding: 30px;
    }

    .wsus__login_area h2 {
        font-size: 30px;
    }

    /* ======LOGIN END====== */


    /* ======MENU DETAILS START====== */
    .wsus__menu_details_text {
        margin-top: 25px;
    }

    .wsus__menu_details_text h2 {
        font-size: 30px;
    }

    .wsus__menu_details_text .rating {
        margin: 12px 0px 15px 0px;
    }

    .wsus__menu_details_text .price {
        font-size: 20px;
        margin-bottom: 10px;
    }

    .wsus__menu_description_area .nav .nav-item .nav-link {
        font-size: 14px;
    }

    .menu_det_description ul li {
        width: 100%;
    }

    .wsus__post_review {
        margin-top: 25px;
    }

    .menu_det_adition_info ul li {
        width: 48% !important;
    }

    .menu_det_description ul li::after {
        top: 7px;
    }

    .wsus__related_menu h2 {
        font-size: 22px;
    }

    /* ======MENU DETAILS END====== */

    .wsus__single_payment {
        height: 85px;
    }

    .wsus__terms_condition h3 {
        font-size: 25px;
    }

    /* ======404 start====== */
    .wsus__404_text img {
        height: 270px !important;
    }

    .wsus__404 div {
        height: auto;
    }

    .wsus__404_text h2 {
        font-size: 30px;
    }

    .wsus__404 {
        height: auto;
        margin-bottom: 70px;
    }

    /* ======404 END====== */

    .wsus__blog_page .wsus__single_blog {
        margin-top: 25px;
        margin-bottom: 0;
    }

    /* ======HOME 2 START====== */
    .wsus__banner_text2 {
        padding-bottom: 0 !important;
    }

    .wsus__banner2 .prevArrow,
    .wsus__banner2 .nextArrow {
        display: none !important;
    }

    .home_2 .main_menu .navbar-nav .nav-item .nav-link {
        color: var(--colorBlack);
    }

    .wsus__menu2 .menu_filter button {
        padding: 5px 20px;
    }

    .wsus__single_team_text2 h4 {
        font-size: 20px;
    }

    .wsus__download2 .wsus__download_img {
        height: 440px !important;
        margin-bottom: 25px;
    }

    .wsus__single_testimonial2 {
        padding: 25px;
    }

    .wsus__blog2 .wsus__single_blog_img {
        height: 200px;
    }

    .wsus__testimonial_text2 h4 {
        font-size: 16px;
    }

    .latest_post li .text {
        width: 80% !important;
    }

    .wsus__testimonial2 .nextArrow,
    .wsus__testimonial2 .prevArrow {
        right: 0px;
    }

    .wsus__testimonial2 .prevArrow {
        left: 0;
    }

    .wsus__subscribe_text h3 {
        font-size: 25px;
    }

    .subscribe_form {
        margin-top: 15px;
    }

    .latest_post li .text {
        width: 68%;
    }

    .footer2 .wsus__footer_bottom {
        margin-top: 20px !important;
    }

    .wsus__footer_social_link {
        width: 100%;
    }

    .wsus__brand2 .wsus__brand_overlay {
        padding: 30px 0px !important;
    }

    .wsus__blog2 .wsus__blog_overlay {
        padding: 65px 0px 65px 0px !important;
    }

    .wsus__menu2 .wsus__menu_item img {
        height: 180px !important;
    }

    .wsus__single_team_img2 {
        height: 250px;
    }

    /* ======HOME 2 END====== */

    .details_extra_item .form-check label {
        max-width: 35%;
    }

    .wsus__banner_img .img {
        height: 350px;
        width: 350px;
        margin: 0 auto;
    }

    .wsus_dashboard_existing_address {
        margin-bottom: -20px;
    }

    .wsus_dashboard_new_address,
    .wsus_dashboard_edit_address {
        padding: 15px;
    }

    .wsus_dashboard_new_address h4,
    .wsus_dashboard_edit_address h4 {
        font-size: 16px;
    }

    .wsus__faq_form {
        margin-top: 25px;
    }

    .wsus__track_order {
        padding-top: 10px;
    }

    .wsus__track_order ul li {
        width: 50%;
        margin-top: 40px;
    }

    .wsus__search_menu_form input {
        margin-bottom: 15px;
    }

    .wsus__search_menu_form button {
        margin-top: 15px;
    }

    .topbar_right {
        justify-content: center;
    }
}


@media (max-width: 575.99px) {

    /* ======HOME PAGE START====== */
    /* topbar start */
    .wsus__topbar {
        position: relative;
    }

    .wsus__topbar_info {
        justify-content: center;
    }

    .wsus__topbar_info li {
        margin: 0px 5px !important;
    }

    .wsus__topbar_info li a {
        font-size: 13px;
    }

    .wsus__topbar_info li a i {
        width: auto;
        height: auto;
        line-height: inherit;
        font-size: 13px;
        margin-right: 5px;
        background: none !important;
        color: var(--colorWhite) !important;
    }

    .topbar_icon {
        justify-content: center;
    }

    .topbar_icon li a {
        margin: 0px 5px;
    }

    /* topbar end */


    /* menu start */
    .main_menu .navbar-brand {
        width: 120px;
        margin-left: 12px;
    }

    .main_menu .navbar-toggler {
        width: 35px;
        height: 35px;
        padding: 0;
        line-height: 35px;
        text-align: center;
        border-radius: 2px;
        background: var(--gradiantBg);
        color: var(--colorWhite);
        border: none;
        margin-right: 12px;
    }

    .main_menu .navbar-toggler:hover {
        background: var(--gradiantHoverBg);
    }

    .main_menu .container {
        padding: 7px 0px 12px 0px;
    }

    .main_menu .navbar-nav {
        text-align: center;
        line-height: 50px;
    }

    .main_menu #navbarNav {
        background: var(--colorWhite);
        margin-top: 15px;
        padding-bottom: 20px;
        padding-top: 10px;
    }

    .main_menu .menu_icon {
        justify-content: center;
        margin-top: 10px;
    }

    .main_menu .menu_icon li:first-child a {
        margin-left: 0;
    }

    .droap_menu {
        z-index: 9;
        max-height: 200px;
        left: 50%;
        text-align: left;
        transform: translateX(-50%);
        -webkit-transform: translateX(-50%);
        -moz-transform: translateX(-50%);
        -ms-transform: translateX(-50%);
        -o-transform: translateX(-50%);
    }

    .wsus__search_form form {
        width: 90%;
    }

    .wsus__menu_cart_area ul li .menu_cart_img {
        width: 55px;
        height: 55px;
    }

    .wsus__menu_cart_area ul li .menu_cart_text {
        width: 75%;
    }

    .wsus__menu_cart_area ul {
        max-height: 340px;
    }

    /* menu end */


    /* banner start */

    .wsus__banner {
        padding-top: 80px;
        height: auto;
    }

    .banner_shape_1 {
        width: 65px;
        height: 60px;
        top: 10px;
        left: 10px;
    }

    .wsus__banner_img {
        margin: 65px 0px 20px 0px;
    }

    .wsus__banner_img .img {
        height: 250px;
        width: 250px;
        margin: 0 auto;
    }

    .wsus__banner_img span {
        width: 80px;
        height: 80px;
        top: -20px;
        font-size: 14px;
        right: 10px;
    }

    .wsus__banner_text h1 {
        font-size: 35px;
        text-align: center;
    }

    .wsus__banner_text h3 {
        font-size: 23px;
        text-align: center;
    }

    .wsus__banner_text p {
        font-size: 16px;
        text-align: center;
    }

    .common_btn {
        padding: 10px 55px 10px 20px !important;
        font-size: 14px !important;
    }

    .wsus__banner_text ul {
        justify-content: center;
    }

    .wsus__banner_text ul li a {
        margin: 0px 5px;
    }

    .wsus__banner_text {
        padding-bottom: 100px;
    }

    .banner_shape_2 {
        width: 40px;
        height: 40px;
        top: 90%;
        left: 10px;
    }

    /* banner end */


    /* why choose start */
    .wsus__choose_single {
        border: none;
    }

    .wsus__choose_single .text h3 {
        font-size: 18px;
    }

    /* why choose end */


    /* daily offer start */
    .banner_shape_3 {
        width: 80px;
        height: 60px;
        top: -40px;
        right: 5%;
    }

    .wsus__section_heading h2 {
        font-size: 22px;
    }

    .wsus__section_heading h4 {
        font-size: 18px;
    }

    .wsus__section_heading p {
        padding: 0;
    }

    .wsus__offer_item_single {
        padding: 15px 90px 15px 15px;
    }

    .wsus__offer_item_single .title {
        font-size: 18px;
    }

    .wsus__offer_item_single span {
        font-size: 16px;
    }

    .wsus__related_menu .nextArrow,
    .wsus__related_menu .prevArrow,
    .wsus__brand .nextArrow,
    .wsus__brand .prevArrow,
    .wsus__offer_item .nextArrow,
    .wsus__offer_item .prevArrow {
        right: 0;
    }

    .wsus__related_menu .prevArrow,
    .wsus__brand .prevArrow,
    .wsus__offer_item .prevArrow {
        right: auto;
        left: 0;
    }

    /* daily offer end */


    /* menu start */
    .wsus__menu .banner_shape_2 {
        width: 60px;
        height: 40px;
        top: -30px;
        right: 5%;
    }

    .wsus__menu .banner_shape_1 {
        width: 65px;
        height: 55px;
        top: -45px;
    }

    .menu_filter button {
        padding: 2px 15px;
        font-weight: 500;
        font-size: 13px;
    }

    .wsus__menu_item .wsus__menu_item_text {
        padding: 165px 15px 15px 15px;
    }

    .wsus__menu_item .wsus__menu_item_text .title {
        font-size: 20px;
    }

    /* menu end */


    /* special offer start */
    .wsus__special_offer_text h3 {
        font-size: 22px;
    }

    .wsus__special_offer_text h5 {
        font-size: 14px;
    }

    .wsus__special_offer_text .simply-section {
        width: 60px;
        height: 70px;
    }

    .wsus__special_offer_text .simply-amount {
        font-size: 22px;
    }

    .wsus__special_offer_text .simply-word {
        font-size: 12px;
    }

    .wsus__team .banner_shape_1 {
        right: 10%;
        width: 65px;
        height: 55px;
    }

    .wsus__team .banner_shape_2 {
        top: 40px;
    }

    .wsus__single_team_text h4 {
        font-size: 20px;
    }

    /* special offer end */


    /* download app start */
    .wsus__download_overlay {
        padding: 20px;
    }

    .wsus__download_text h4,
    .wsus__download_text h2,
    .wsus__download_text span,
    .wsus__download_text p {
        text-align: center !important;
    }

    .wsus__download_text ul {
        justify-content: center;
    }

    .wsus__download_text ul li a {
        margin-right: 0;
        margin-bottom: 10px;
    }

    .wsus__download_img {
        height: 220px;
        margin-top: 30px;
    }

    /* download app end */


    /* counter start */
    .wsus__single_counter {
        justify-content: start;
        margin-bottom: 30px;
    }

    .wsus__single_counter i {
        font-size: 40px;
    }

    .wsus__single_counter .text h2 {
        font-size: 30px;
    }

    .wsus__single_counter .text p {
        font-size: 15px;
    }

    .wsus__single_counter .text h2::after {
        font-size: 30px;
        right: -20px;
    }

    .wsus__counter_overlay {
        padding-bottom: 45px !important;
    }

    /* counter end */


    /* testimonial start */
    .wsus__testimonial_product {
        width: 75px;
        height: 75px;
    }

    .wsus__testimonial_header .text h4 {
        font-size: 18px;
    }

    /* testimonial end */


    /* blog start */
    .wsus__single_blog_img {
        height: 350px;
    }

    .wsus__single_blog_text {
        padding: 20px;
    }

    .wsus__single_blog_text .title {
        font-size: 18px;
        margin-top: 10px;
    }

    .wsus__single_blog_text .category {
        left: 20px;
    }

    .wsus__single_blog_text ul li {
        margin-bottom: 5px;
    }

    /* blog end */


    /* footer start */
    .wsus__footer_content {
        margin-bottom: 50px;
    }

    .wsus__footer_content h3 {
        margin-bottom: 5px;
    }

    .footer_overlay {
        padding-bottom: 18px !important;
    }

    .wsus__footer_bottom_text p {
        text-align: center;
        width: 100%;
    }

    .wsus__footer_bottom_text ul {
        justify-content: center;
        margin-top: 20px;
        width: 100%;
    }

    .wsus__scroll_btn {
        bottom: 35px;
        right: -25px;
    }

    .wsus__breadcrumb_text ul li a {
        font-size: 14px;
    }

    .wsus__breadcrumb_text ul li:first-child a::before {
        font-size: 14px;
    }

    /* footer end */
    /* ======HOME PAGE END====== */


    /* ======ABOUT PAGE START====== */
    .wsus__breadcrumb_text h1 {
        font-size: 25px;
    }

    .wsus__breadcrumb_overlay {
        padding: 155px 0px 87px 0px;
    }

    .wsus__about_us_img {
        height: 300px;
        margin-bottom: 25px;
        margin-right: 0;
    }

    .wsus__about_us_text ul li {
        width: 100%;
    }

    .wsus__about_video_text p {
        font-size: 20px;
        margin-right: 30px;
    }

    .wsus__about_video_overlay {
        padding: 80px 0px;
    }

    .wsus__about_choose_img .img_1 {
        width: 260px;
        height: 370px;
    }

    .wsus__about_choose_img .img_2 {
        width: 200px;
        height: 150px;
        right: 0;
    }

    .wsus__about_choose_img .img_3 {
        width: 200px;
        height: 150px;
    }

    .wsus__about_choose_text {
        padding: 15px;
        margin-top: 10px;
    }

    .wsus__about_choose_text ul li {
        width: 100%;
    }

    /* ======ABOUT PAGE END====== */


    /* ======BLOG DETAILS START====== */
    .wsus__blog_details_img {
        height: 250px;
    }

    .wsus__blog_details_text {
        padding: 15px;
    }

    .wsus__blog_details_text .details_bloger li {
        width: 100%;
        margin: 0;
        margin-bottom: 5px;
    }

    .wsus__blog_details_text .details_bloger {
        padding-bottom: 10px;
    }

    .wsus__blog_details_text h1,
    .wsus__blog_details_text h2 {
        font-size: 25px;
    }

    .wsus__blog_quot_text {
        padding: 15px 15px 15px 35px;
    }

    .wsus__blog_quot_text .left_icon {
        left: 5px;
        top: 10px;
        width: 25px;
    }

    .wsus__blog_quot_text p span {
        margin-left: 10px;
        width: 25px;
    }

    .wsus__blog_quot_text h4 {
        font-size: 16px;
    }

    .wsus__blog_quot_text h4 span {
        margin-left: 0;
        margin-top: 5px;
    }

    .wsus__blog_details_text h3 {
        font-size: 20px;
    }

    .blog_tags_share .tags {
        margin-bottom: 15px;
    }

    .blog_det_button li {
        width: 100%;
        padding: 15px;
    }

    .blog_det_button li a img {
        height: 110px !important;
    }

    .blog_det_button li a p {
        font-size: 16px;
        width: 55%;
    }

    .blog_det_button li:first-child {
        margin-bottom: 25px;
    }

    .blog_det_button li a p span {
        font-size: 16px;
    }

    .wsus__comment {
        padding: 15px;
    }

    .wsus__single_comm_text {
        width: 100%;
    }

    .wsus__single_comm_text h3 span {
        width: 100%;
        margin-top: 10px;
    }

    .wsus__single_comm_text h3 {
        margin-top: 10px !important;
    }

    .wsus__single_comment.replay {
        padding-left: 30px;
    }

    .wsus__single_comment img {
        width: 80px !important;
        height: 80px !important;
    }

    .comment_input {
        padding: 15px;
        margin-bottom: 25px;
    }

    .comment_input h4 {
        font-size: 20px;
    }

    .blog_sidebar {
        padding: 15px;
    }

    .blog_sidebar h3 {
        font-size: 18px;
    }

    .wsus__related_blog ul li .text {
        width: 65%;
    }

    /* ======BLOG DETAILS END====== */

    .wsus__cart_list {
        margin-bottom: 25px;
    }

    .wsus__check_form {
        margin-bottom: 25px;
        padding: 20px 20px 0px 20px;
    }

    .wsus__pagination ul li a {
        width: 30px !important;
        height: 30px !important;
        line-height: 30px !important;
        font-size: 14px !important;
    }

    /* ======CONTACT START====== */
    .wsus__contact_form {
        padding: 15px;
        margin-bottom: 25px;
    }

    .wsus__contact_map {
        height: 300px;
    }

    /* ======CONTACT END====== */


    /* ======DASHBOARD START====== */
    .wsus__dashboard_area {
        border: none;
    }

    .wsus__dashboard_content {
        padding: 0 !important;
    }

    .wsus_dash_personal_info {
        border-radius: 0;
        margin: 0;
        padding: 15px;
    }

    .wsus_dashboard_body h3 {
        font-size: 20px;
        margin-top: 25px;
    }

    .wsus_dash_personal_info h4 {
        font-size: 16px;
    }

    .wsus_dash_personal_info p {
        flex-wrap: wrap;
    }

    .wsus_dash_personal_info p span {
        min-width: 100%;
        margin-bottom: 5px;
    }

    .wsus_dash_personal_info.show .wsus_dash_personal_info_edit {
        margin: 0;
    }

    .wsus_dashboard_order table {
        border-radius: 0;
    }

    .wsus_dashboard_order table tr th,
    .wsus_dashboard_order table tr td {
        width: 140px;
    }

    .wsus_dashboard_body .wsus__invoice {
        border-radius: 0px;
        padding: 15px;
        -webkit-border-radius: 0px;
        -moz-border-radius: 0px;
        -ms-border-radius: 0px;
        -o-border-radius: 0px;
    }

    .wsus__invoice_header .header_address {
        max-width: 100%;
        margin-top: 20px;
    }

    .wsus__invoice_header {
        margin-top: 15px;
    }

    .wsus__invoice .sl_no {
        min-width: 80px;
    }

    .wsus__invoice .package {
        min-width: 180px;
    }

    .wsus__invoice .price,
    .wsus__invoice .qnty,
    .wsus__invoice .total {
        min-width: 100px;
    }

    .wsus__invoice_header .header_address h4 {
        font-size: 18px;
    }

    .wsus__single_comm_text .rating {
        margin-top: 10px;
    }

    .wsus__single_comment.replay .wsus__single_comm_text {
        width: 100%;
    }

    /* ======DASHBOARD END====== */

    /* ======LOGIN START====== */
    .wsus__login_area {
        padding: 20px;
    }

    .wsus__login_area h2 {
        font-size: 25px;
    }

    /* ======LOGIN END====== */


    /* ======MENU DETAILS START====== */
    .wsus__menu_details_text {
        margin-top: 25px;
    }

    .wsus__menu_details_text h2 {
        font-size: 25px;
    }

    .wsus__menu_details_text .rating {
        margin: 12px 0px 15px 0px;
    }

    .wsus__menu_details_text .price {
        font-size: 20px;
        margin-bottom: 10px;
    }

    .details_button_area li a {
        margin-right: 10px;
    }

    .wsus__menu_description_area .nav .nav-item {
        width: 100%;
        border-right: 0;
        border-bottom: 1px solid var(--colorPrimary);
    }

    .menu_det_description ul li {
        width: 100%;
    }

    .wsus__post_review {
        margin-top: 25px;
    }

    .menu_det_description ul li::after {
        top: 7px;
    }

    .wsus__related_menu h2 {
        font-size: 22px;
    }

    /* ======MENU DETAILS END====== */

    .wsus__single_payment {
        height: 80px;
    }

    .wsus__terms_condition h3 {
        font-size: 20px;
    }

    /* ======404 start====== */
    .wsus__404_text img {
        height: 180px !important;
    }

    .wsus__404 div {
        height: auto;
    }

    .wsus__404_text h2 {
        font-size: 30px;
    }

    .wsus__404 {
        height: auto;
        margin-bottom: 70px;
    }

    /* ======404 END====== */

    .wsus__blog_page .wsus__single_blog {
        margin-top: 25px;
        margin-bottom: 0;
    }

    /* ======HOME 2 START====== */
    .wsus__banner_text2 {
        padding-bottom: 0 !important;
    }

    .wsus__banner2 .prevArrow,
    .wsus__banner2 .nextArrow {
        display: none !important;
    }

    .home_2 .main_menu .navbar-nav .nav-item .nav-link {
        color: var(--colorBlack);
    }

    .wsus__menu2 .menu_filter button {
        padding: 5px 20px;
    }

    .wsus__single_team_text2 h4 {
        font-size: 20px;
    }

    .wsus__download2 .wsus__download_img {
        height: 220px !important;
        text-align: center;
        margin-bottom: 25px;
    }

    .wsus__single_testimonial2 {
        padding: 25px;
    }

    .wsus__testimonial_product2 {
        display: none;
    }

    .wsus__testimonial_text2 {
        min-width: 100%;
        padding-left: 0;
        margin-left: 0;
        border-left: 0;
        text-align: center;
    }

    .wsus__testimonial_text2 .rating {
        text-align: center !important;
    }

    .wsus__testimonial_text2 h4 {
        font-size: 16px;
        text-align: center;
    }

    .wsus__testimonial_text2 .client_img {
        margin: 0 auto;
        margin-bottom: 10px;
    }

    .wsus__testimonial2 .nextArrow,
    .wsus__testimonial2 .prevArrow {
        right: 0px;
    }

    .wsus__testimonial2 .prevArrow {
        left: 0;
    }

    .wsus__subscribe_text h3 {
        font-size: 25px;
    }

    .subscribe_form {
        margin-top: 15px;
    }

    .latest_post li .text a {
        font-size: 16px;
    }

    .latest_post li .text {
        width: 68%;
    }

    .footer2 .wsus__footer_bottom {
        margin-top: 20px !important;
    }

    .footer2 .footer_overlay {
        padding-bottom: 0 !important;
    }

    .wsus__footer_social_link {
        width: 100%;
    }

    .wsus__brand2 .wsus__brand_overlay {
        padding: 30px 0px !important;
    }

    .wsus__blog2 .wsus__blog_overlay {
        padding: 65px 0px 65px 0px !important;
    }

    .wsus__offer_item2 .wsus__offer_item_single .img {
        width: 100px;
        height: 100px;
    }

    /* ======HOME 2 END====== */

    .details_size .form-check label,
    .details_extra_item .form-check label {
        min-width: 65%;
    }

    .add_slider .nextArrow {
        right: 0;
    }

    .add_slider .prevArrow {
        left: 0;
    }

    .wsus__checkout_form h5,
    .wsus__checkout_form h5 a {
        font-size: 15px;
    }

    .wsus_dashboard_existing_address {
        margin-bottom: -20px;
    }

    .wsus_dashboard_new_address,
    .wsus_dashboard_edit_address {
        padding: 15px;
    }

    .wsus_dashboard_new_address h4,
    .wsus_dashboard_edit_address h4 {
        font-size: 16px;
    }

    .wsus__track_order {
        padding-top: 10px;
    }

    .wsus__track_order ul li {
        width: 100%;
        margin-top: 40px;
    }

    .wsus__search_menu_form input {
        margin-bottom: 15px;
    }

    .wsus__search_menu_form button {
        margin-top: 15px;
    }

    .wsus__menu_cart_boody {
        width: 100%;
    }

    .main_menu .navbar-nav .nav-item .nav-link {
        text-align: center;
    }

    .topbar_language {
        margin-right: 15px;
    }

}