@extends('admin.admin-layout')

@section('content')
<div class="row">
    <!-- Creative Header -->
    <div class="col-12">
        <div class="admin-creative-header mb-4">
            <div class="admin-header-content">
                <div class="admin-header-main">
                    <div class="admin-header-icon">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="admin-header-text">
                        <h1 class="admin-header-title">{{ $teamMember->name }}</h1>
                        <p class="admin-header-subtitle">{{ $teamMember->position }}</p>
                    </div>
                </div>
                <div class="admin-header-actions">
                    <a href="{{ route('admin.team-members.edit', $teamMember) }}" class="admin-btn admin-btn-warning">
                        <i class="fas fa-edit"></i> Edit Member
                    </a>
                    <a href="{{ route('admin.team-members.index') }}" class="admin-btn admin-btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-4">
        <div class="admin-profile-card">
            <div class="admin-profile-image">
                <img src="{{ asset('storage/' . $teamMember->image) }}" alt="{{ $teamMember->name }}" class="img-fluid">
                <div class="admin-profile-status">
                    <span class="admin-status-badge {{ $teamMember->is_active ? 'active' : 'inactive' }}">
                        <i class="fas {{ $teamMember->is_active ? 'fa-check-circle' : 'fa-times-circle' }}"></i>
                        {{ $teamMember->is_active ? 'Active' : 'Inactive' }}
                    </span>
                </div>
            </div>
            
            <div class="admin-profile-info">
                <h3 class="admin-profile-name">{{ $teamMember->name }}</h3>
                <p class="admin-profile-position">{{ $teamMember->position }}</p>
                
                @if($teamMember->facebook_url || $teamMember->linkedin_url || $teamMember->twitter_url || $teamMember->instagram_url)
                <div class="admin-profile-social">
                    @if($teamMember->facebook_url)
                        <a href="{{ $teamMember->facebook_url }}" target="_blank" class="admin-social-link facebook">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                    @endif
                    @if($teamMember->linkedin_url)
                        <a href="{{ $teamMember->linkedin_url }}" target="_blank" class="admin-social-link linkedin">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                    @endif
                    @if($teamMember->twitter_url)
                        <a href="{{ $teamMember->twitter_url }}" target="_blank" class="admin-social-link twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                    @endif
                    @if($teamMember->instagram_url)
                        <a href="{{ $teamMember->instagram_url }}" target="_blank" class="admin-social-link instagram">
                            <i class="fab fa-instagram"></i>
                        </a>
                    @endif
                </div>
                @endif
            </div>
        </div>
        
        <div class="admin-quick-actions-card">
            <div class="admin-quick-actions-header">
                <h4><i class="fas fa-bolt"></i> Quick Actions</h4>
            </div>
            <div class="admin-quick-actions-content">
                <div class="admin-quick-action">
                    <button type="button" class="admin-btn admin-btn-sm admin-btn-info w-100" 
                            onclick="toggleStatus({{ $teamMember->id }}, '{{ route('admin.team-members.toggle-status', $teamMember) }}')">
                        <i class="fas fa-toggle-{{ $teamMember->is_active ? 'off' : 'on' }}"></i>
                        {{ $teamMember->is_active ? 'Deactivate' : 'Activate' }} Member
                    </button>
                </div>
                
                <div class="admin-quick-action">
                    <a href="{{ route('admin.team-members.edit', $teamMember) }}" class="admin-btn admin-btn-sm admin-btn-warning w-100">
                        <i class="fas fa-edit"></i> Edit Information
                    </a>
                </div>
                
                <div class="admin-quick-action">
                    <form action="{{ route('admin.team-members.destroy', $teamMember) }}" method="POST" class="d-inline w-100">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="admin-btn admin-btn-sm admin-btn-danger w-100" 
                                onclick="return confirm('Are you sure you want to delete this team member? This action cannot be undone.')">
                            <i class="fas fa-trash"></i> Delete Member
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-8">
        <div class="admin-details-card">
            <div class="admin-details-header">
                <h4><i class="fas fa-info-circle"></i> Member Details</h4>
            </div>
            <div class="admin-details-content">
                <div class="row">
                    <div class="col-md-6">
                        <div class="admin-detail-item">
                            <label class="admin-detail-label">Full Name</label>
                            <div class="admin-detail-value">{{ $teamMember->name }}</div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="admin-detail-item">
                            <label class="admin-detail-label">Position/Role</label>
                            <div class="admin-detail-value">{{ $teamMember->position }}</div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="admin-detail-item">
                            <label class="admin-detail-label">Status</label>
                            <div class="admin-detail-value">
                                <span class="admin-status-badge {{ $teamMember->is_active ? 'active' : 'inactive' }}">
                                    <i class="fas {{ $teamMember->is_active ? 'fa-check-circle' : 'fa-times-circle' }}"></i>
                                    {{ $teamMember->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="admin-detail-item">
                            <label class="admin-detail-label">Sort Order</label>
                            <div class="admin-detail-value">{{ $teamMember->sort_order }}</div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="admin-detail-item">
                            <label class="admin-detail-label">Created</label>
                            <div class="admin-detail-value">{{ $teamMember->created_at->format('M d, Y \a\t g:i A') }}</div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="admin-detail-item">
                            <label class="admin-detail-label">Last Updated</label>
                            <div class="admin-detail-value">{{ $teamMember->updated_at->format('M d, Y \a\t g:i A') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="admin-social-links-card">
            <div class="admin-social-links-header">
                <h4><i class="fab fa-facebook"></i> Social Media Links</h4>
            </div>
            <div class="admin-social-links-content">
                @if($teamMember->facebook_url || $teamMember->linkedin_url || $teamMember->twitter_url || $teamMember->instagram_url)
                    <div class="row">
                        @if($teamMember->facebook_url)
                        <div class="col-md-6">
                            <div class="admin-social-item">
                                <div class="admin-social-icon facebook">
                                    <i class="fab fa-facebook-f"></i>
                                </div>
                                <div class="admin-social-info">
                                    <label>Facebook</label>
                                    <a href="{{ $teamMember->facebook_url }}" target="_blank" class="admin-social-url">
                                        {{ $teamMember->facebook_url }}
                                    </a>
                                </div>
                            </div>
                        </div>
                        @endif
                        
                        @if($teamMember->linkedin_url)
                        <div class="col-md-6">
                            <div class="admin-social-item">
                                <div class="admin-social-icon linkedin">
                                    <i class="fab fa-linkedin-in"></i>
                                </div>
                                <div class="admin-social-info">
                                    <label>LinkedIn</label>
                                    <a href="{{ $teamMember->linkedin_url }}" target="_blank" class="admin-social-url">
                                        {{ $teamMember->linkedin_url }}
                                    </a>
                                </div>
                            </div>
                        </div>
                        @endif
                        
                        @if($teamMember->twitter_url)
                        <div class="col-md-6">
                            <div class="admin-social-item">
                                <div class="admin-social-icon twitter">
                                    <i class="fab fa-twitter"></i>
                                </div>
                                <div class="admin-social-info">
                                    <label>Twitter</label>
                                    <a href="{{ $teamMember->twitter_url }}" target="_blank" class="admin-social-url">
                                        {{ $teamMember->twitter_url }}
                                    </a>
                                </div>
                            </div>
                        </div>
                        @endif
                        
                        @if($teamMember->instagram_url)
                        <div class="col-md-6">
                            <div class="admin-social-item">
                                <div class="admin-social-icon instagram">
                                    <i class="fab fa-instagram"></i>
                                </div>
                                <div class="admin-social-info">
                                    <label>Instagram</label>
                                    <a href="{{ $teamMember->instagram_url }}" target="_blank" class="admin-social-url">
                                        {{ $teamMember->instagram_url }}
                                    </a>
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>
                @else
                    <div class="admin-empty-social">
                        <div class="admin-empty-social-icon">
                            <i class="fas fa-link"></i>
                        </div>
                        <h5>No Social Media Links</h5>
                        <p>This team member hasn't added any social media links yet.</p>
                        <a href="{{ route('admin.team-members.edit', $teamMember) }}" class="admin-btn admin-btn-sm admin-btn-primary">
                            <i class="fas fa-plus"></i> Add Social Links
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
<style>
.admin-profile-card {
    background: var(--cardBg);
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 20px;
}

.admin-profile-image {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.admin-profile-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.admin-profile-status {
    position: absolute;
    top: 15px;
    right: 15px;
}

.admin-status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.admin-status-badge.active {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.admin-status-badge.inactive {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.admin-profile-info {
    padding: 25px;
    text-align: center;
}

.admin-profile-name {
    margin: 0 0 8px 0;
    color: var(--headingColor);
    font-size: 24px;
    font-weight: 700;
}

.admin-profile-position {
    margin: 0 0 20px 0;
    color: var(--paraColor);
    font-size: 16px;
    font-weight: 500;
}

.admin-profile-social {
    display: flex;
    justify-content: center;
    gap: 12px;
}

.admin-social-link {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 18px;
}

.admin-social-link:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
    color: white;
}

.admin-social-link.facebook { background: #3b5998; }
.admin-social-link.linkedin { background: #0077b5; }
.admin-social-link.twitter { background: #1da1f2; }
.admin-social-link.instagram { background: #e4405f; }

.admin-quick-actions-card {
    background: var(--cardBg);
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 20px;
}

.admin-quick-actions-header {
    background: var(--primaryColor);
    color: white;
    padding: 15px 20px;
}

.admin-quick-actions-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.admin-quick-actions-content {
    padding: 20px;
}

.admin-quick-action {
    margin-bottom: 12px;
}

.admin-quick-action:last-child {
    margin-bottom: 0;
}

.admin-details-card {
    background: var(--cardBg);
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 20px;
}

.admin-details-header {
    background: linear-gradient(135deg, var(--primaryColor), var(--secondaryColor));
    color: white;
    padding: 20px 25px;
}

.admin-details-header h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.admin-details-content {
    padding: 25px;
}

.admin-detail-item {
    margin-bottom: 20px;
}

.admin-detail-label {
    display: block;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    color: var(--paraColor);
    margin-bottom: 5px;
    letter-spacing: 0.5px;
}

.admin-detail-value {
    font-size: 16px;
    color: var(--headingColor);
    font-weight: 500;
}

.admin-social-links-card {
    background: var(--cardBg);
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.admin-social-links-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 20px 25px;
}

.admin-social-links-header h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.admin-social-links-content {
    padding: 25px;
}

.admin-social-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 15px;
}

.admin-social-item:last-child {
    margin-bottom: 0;
}

.admin-social-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.admin-social-icon.facebook { background: #3b5998; }
.admin-social-icon.linkedin { background: #0077b5; }
.admin-social-icon.twitter { background: #1da1f2; }
.admin-social-icon.instagram { background: #e4405f; }

.admin-social-info {
    flex: 1;
}

.admin-social-info label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: var(--headingColor);
    margin-bottom: 3px;
}

.admin-social-url {
    color: var(--primaryColor);
    text-decoration: none;
    font-size: 14px;
    word-break: break-all;
}

.admin-social-url:hover {
    text-decoration: underline;
}

.admin-empty-social {
    text-align: center;
    padding: 40px 20px;
    color: var(--paraColor);
}

.admin-empty-social-icon {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 20px;
}

.admin-empty-social h5 {
    margin: 0 0 10px 0;
    color: var(--headingColor);
}

.admin-empty-social p {
    margin: 0 0 20px 0;
    font-size: 14px;
}
</style>

<script>
function toggleStatus(memberId, url) {
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Reload the page to reflect changes
            location.reload();
        } else {
            alert('Error updating status. Please try again.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating status. Please try again.');
    });
}
</script>
@endsection
