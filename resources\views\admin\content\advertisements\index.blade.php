@extends('admin.admin-layout')

@section('content')
<div class="row">
    <!-- Creative Header -->
    <div class="col-12">
        <div class="admin-creative-header mb-4">
            <div class="admin-header-content">
                <div class="admin-header-main">
                    <div class="admin-header-icon">
                        <i class="fas fa-ad"></i>
                    </div>
                    <div class="admin-header-text">
                        <h1 class="admin-header-title">Advertisements</h1>
                        <p class="admin-header-subtitle">Manage promotional banners and advertisement content</p>
                    </div>
                </div>
                <div class="admin-header-actions">
                    <a href="{{ route('admin.advertisements.create') }}" class="admin-btn">
                        <i class="fas fa-plus"></i> Add New Ad
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="admin-table-wrapper">
            <div class="admin-table-header">
                <h3 class="admin-table-title">All Advertisements</h3>
                <div class="admin-table-actions">
                    <span class="admin-badge admin-badge-info">{{ $ads->count() }} Total</span>
                </div>
            </div>
            
            @if($ads->count() > 0)
                <div class="row">
                    @foreach($ads as $ad)
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="admin-ad-card">
                            <div class="admin-ad-image">
                                <img src="{{ asset('storage/' . $ad->image) }}" alt="{{ $ad->title }}">
                                <div class="admin-ad-status">
                                    <div class="admin-toggle-switch">
                                        <input type="checkbox" id="status-{{ $ad->id }}" 
                                               {{ $ad->is_active ? 'checked' : '' }}
                                               onchange="toggleStatus({{ $ad->id }}, '{{ route('admin.advertisements.toggle-status', $ad) }}')">
                                        <label for="status-{{ $ad->id }}"></label>
                                    </div>
                                </div>
                                <div class="admin-ad-overlay">
                                    <div class="admin-ad-overlay-content">
                                        <h5>{{ $ad->title }}</h5>
                                        @if($ad->url)
                                            <a href="{{ $ad->url }}" target="_blank" class="admin-ad-link">
                                                <i class="fas fa-external-link-alt"></i>
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            
                            <div class="admin-ad-content">
                                <h4 class="admin-ad-title">{{ $ad->title }}</h4>
                                <p class="admin-ad-description">{{ Str::limit($ad->description, 80) }}</p>
                                
                                <div class="admin-ad-meta">
                                    <span class="admin-badge admin-badge-secondary">Order: {{ $ad->sort_order }}</span>
                                    @if($ad->url)
                                        <span class="admin-badge admin-badge-info">Has Link</span>
                                    @endif
                                </div>
                                
                                <div class="admin-ad-actions">
                                    <a href="{{ route('admin.advertisements.show', $ad) }}" class="admin-btn admin-btn-sm admin-btn-info">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    <a href="{{ route('admin.advertisements.edit', $ad) }}" class="admin-btn admin-btn-sm admin-btn-warning">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    <form action="{{ route('admin.advertisements.destroy', $ad) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="admin-btn admin-btn-sm admin-btn-danger" 
                                                onclick="return confirm('Are you sure you want to delete this advertisement?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            @else
                <div class="admin-empty-state">
                    <div class="admin-empty-icon">
                        <i class="fas fa-ad"></i>
                    </div>
                    <h3>No Advertisements Found</h3>
                    <p>Create your first advertisement to promote special offers and products.</p>
                    <a href="{{ route('admin.advertisements.create') }}" class="admin-btn">
                        <i class="fas fa-plus"></i> Create First Ad
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<style>
.admin-ad-card {
    background: var(--cardBg);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}

.admin-ad-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.admin-ad-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.admin-ad-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.admin-ad-card:hover .admin-ad-image img {
    transform: scale(1.05);
}

.admin-ad-status {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 2;
}

.admin-ad-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0,0,0,0.7), rgba(0,0,0,0.3));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.admin-ad-card:hover .admin-ad-overlay {
    opacity: 1;
}

.admin-ad-overlay-content {
    text-align: center;
    color: white;
}

.admin-ad-overlay-content h5 {
    margin: 0 0 10px 0;
    font-weight: 600;
}

.admin-ad-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--primaryColor);
    color: white;
    border-radius: 50%;
    text-decoration: none;
    transition: background 0.3s ease;
}

.admin-ad-link:hover {
    background: #ff6b6b;
    color: white;
    text-decoration: none;
}

.admin-ad-content {
    padding: 20px;
}

.admin-ad-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--headingColor);
    margin-bottom: 10px;
}

.admin-ad-description {
    color: var(--paraColor);
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 15px;
}

.admin-ad-meta {
    margin-bottom: 20px;
}

.admin-ad-meta .admin-badge {
    margin-right: 8px;
    margin-bottom: 5px;
}

.admin-ad-actions {
    display: flex;
    gap: 8px;
}

.admin-ad-actions .admin-btn {
    flex: 1;
    text-align: center;
}
</style>

<script>
function toggleStatus(id, url) {
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}
</script>
@endsection
