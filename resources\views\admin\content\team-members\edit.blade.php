@extends('admin.admin-layout')

@section('content')
<div class="row">
    <!-- Creative Header -->
    <div class="col-12">
        <div class="admin-creative-header mb-4">
            <div class="admin-header-content">
                <div class="admin-header-main">
                    <div class="admin-header-icon">
                        <i class="fas fa-user-edit"></i>
                    </div>
                    <div class="admin-header-text">
                        <h1 class="admin-header-title">Edit Team Member</h1>
                        <p class="admin-header-subtitle">Update "{{ $teamMember->name }}" information</p>
                    </div>
                </div>
                <div class="admin-header-actions">
                    <a href="{{ route('admin.team-members.show', $teamMember) }}" class="admin-btn admin-btn-info">
                        <i class="fas fa-eye"></i> View Details
                    </a>
                    <a href="{{ route('admin.team-members.index') }}" class="admin-btn admin-btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="admin-form-wrapper">
            <div class="admin-form-header">
                <h3 class="admin-form-title">Team Member Information</h3>
            </div>
            
            <form action="{{ route('admin.team-members.update', $teamMember) }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="admin-form-group">
                            <label for="name" class="admin-form-label">Full Name *</label>
                            <input type="text" class="admin-form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name', $teamMember->name) }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="admin-form-group">
                            <label for="position" class="admin-form-label">Position/Role *</label>
                            <input type="text" class="admin-form-control @error('position') is-invalid @enderror" 
                                   id="position" name="position" value="{{ old('position', $teamMember->position) }}" 
                                   placeholder="e.g., Senior Chef, Sous Chef, Manager" required>
                            @error('position')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
                
                <div class="admin-form-group">
                    <label for="image" class="admin-form-label">Profile Image</label>
                    <div class="admin-file-upload">
                        <input type="file" class="admin-file-input @error('image') is-invalid @enderror" 
                               id="image" name="image" accept="image/*">
                        <label for="image" class="admin-file-label" id="fileLabel">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <span>Choose New Image or Drag & Drop</span>
                        </label>
                        <div class="admin-file-preview" id="imagePreview">
                            <img id="previewImg" src="{{ asset('storage/' . $teamMember->image) }}" alt="Current Image">
                            <button type="button" class="admin-file-remove" onclick="removeImage()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    @error('image')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <small class="form-text text-muted">
                        Leave empty to keep current image. Supported formats: JPEG, PNG, JPG, GIF. Max size: 2MB
                    </small>
                </div>
                
                <div class="admin-form-section">
                    <h4 class="admin-form-section-title">
                        <i class="fab fa-facebook"></i> Social Media Links
                        <small>(Optional)</small>
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="admin-form-group">
                                <label for="facebook_url" class="admin-form-label">
                                    <i class="fab fa-facebook-f"></i> Facebook URL
                                </label>
                                <input type="url" class="admin-form-control @error('facebook_url') is-invalid @enderror" 
                                       id="facebook_url" name="facebook_url" value="{{ old('facebook_url', $teamMember->facebook_url) }}" 
                                       placeholder="https://www.facebook.com/username">
                                @error('facebook_url')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="admin-form-group">
                                <label for="linkedin_url" class="admin-form-label">
                                    <i class="fab fa-linkedin-in"></i> LinkedIn URL
                                </label>
                                <input type="url" class="admin-form-control @error('linkedin_url') is-invalid @enderror" 
                                       id="linkedin_url" name="linkedin_url" value="{{ old('linkedin_url', $teamMember->linkedin_url) }}" 
                                       placeholder="https://www.linkedin.com/in/username">
                                @error('linkedin_url')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="admin-form-group">
                                <label for="twitter_url" class="admin-form-label">
                                    <i class="fab fa-twitter"></i> Twitter URL
                                </label>
                                <input type="url" class="admin-form-control @error('twitter_url') is-invalid @enderror" 
                                       id="twitter_url" name="twitter_url" value="{{ old('twitter_url', $teamMember->twitter_url) }}" 
                                       placeholder="https://www.twitter.com/username">
                                @error('twitter_url')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="admin-form-group">
                                <label for="instagram_url" class="admin-form-label">
                                    <i class="fab fa-instagram"></i> Instagram URL
                                </label>
                                <input type="url" class="admin-form-control @error('instagram_url') is-invalid @enderror" 
                                       id="instagram_url" name="instagram_url" value="{{ old('instagram_url', $teamMember->instagram_url) }}" 
                                       placeholder="https://www.instagram.com/username">
                                @error('instagram_url')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="admin-form-group">
                            <label for="sort_order" class="admin-form-label">Sort Order</label>
                            <input type="number" class="admin-form-control @error('sort_order') is-invalid @enderror" 
                                   id="sort_order" name="sort_order" value="{{ old('sort_order', $teamMember->sort_order) }}" min="0">
                            @error('sort_order')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">Lower numbers appear first</small>
                        </div>
                    </div>
                </div>
                
                <div class="admin-form-actions">
                    <button type="submit" class="admin-btn admin-btn-primary">
                        <i class="fas fa-save"></i> Update Team Member
                    </button>
                    <a href="{{ route('admin.team-members.index') }}" class="admin-btn admin-btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="admin-current-image-card">
            <div class="admin-current-image-header">
                <h4><i class="fas fa-image"></i> Current Image</h4>
            </div>
            <div class="admin-current-image-content">
                <div class="current-image-display">
                    <img src="{{ asset('storage/' . $teamMember->image) }}" alt="{{ $teamMember->name }}" class="img-fluid">
                </div>
                <div class="current-image-info">
                    <p><strong>Uploaded:</strong> {{ $teamMember->created_at->format('M d, Y') }}</p>
                    <p><strong>Last Updated:</strong> {{ $teamMember->updated_at->format('M d, Y') }}</p>
                </div>
            </div>
        </div>
        
        <div class="admin-preview-card">
            <div class="admin-preview-header">
                <h4><i class="fas fa-eye"></i> Live Preview</h4>
            </div>
            <div class="admin-preview-content">
                <div class="team-member-preview">
                    <div class="preview-image">
                        <img id="preview-member-image" src="{{ asset('storage/' . $teamMember->image) }}" alt="Preview">
                    </div>
                    <div class="preview-info">
                        <h5 id="preview-name">{{ $teamMember->name }}</h5>
                        <p id="preview-position">{{ $teamMember->position }}</p>
                        <div class="preview-social">
                            <a href="{{ $teamMember->facebook_url }}" id="preview-facebook" 
                               style="{{ $teamMember->facebook_url ? 'display: flex;' : 'display: none;' }}">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="{{ $teamMember->linkedin_url }}" id="preview-linkedin" 
                               style="{{ $teamMember->linkedin_url ? 'display: flex;' : 'display: none;' }}">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <a href="{{ $teamMember->twitter_url }}" id="preview-twitter" 
                               style="{{ $teamMember->twitter_url ? 'display: flex;' : 'display: none;' }}">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="{{ $teamMember->instagram_url }}" id="preview-instagram" 
                               style="{{ $teamMember->instagram_url ? 'display: flex;' : 'display: none;' }}">
                                <i class="fab fa-instagram"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="admin-status-card">
            <div class="admin-status-header">
                <h4><i class="fas fa-toggle-on"></i> Status Settings</h4>
            </div>
            <div class="admin-status-content">
                <div class="admin-form-group">
                    <div class="admin-checkbox-wrapper">
                        <input type="checkbox" id="is_active" name="is_active" value="1" 
                               {{ old('is_active', $teamMember->is_active) ? 'checked' : '' }}>
                        <label for="is_active" class="admin-checkbox-label">
                            <span class="admin-checkbox-text">Active</span>
                            <small class="admin-checkbox-help">Enable to show this member on website</small>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<style>
.admin-form-section {
    margin: 30px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid var(--primaryColor);
}

.admin-form-section-title {
    margin: 0 0 20px 0;
    color: var(--headingColor);
    font-size: 16px;
    font-weight: 600;
}

.admin-form-section-title small {
    color: var(--paraColor);
    font-weight: normal;
    font-size: 12px;
}

.admin-file-upload {
    position: relative;
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.admin-file-upload:hover {
    border-color: var(--primaryColor);
    background: rgba(var(--primaryColorRgb), 0.05);
}

.admin-file-input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.admin-file-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    color: var(--paraColor);
}

.admin-file-label i {
    font-size: 32px;
    color: var(--primaryColor);
}

.admin-file-preview {
    position: relative;
    max-width: 200px;
    margin: 0 auto;
}

.admin-file-preview img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 8px;
}

.admin-file-remove {
    position: absolute;
    top: -10px;
    right: -10px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.admin-current-image-card {
    background: var(--cardBg);
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.admin-current-image-header {
    background: var(--primaryColor);
    color: white;
    padding: 15px 20px;
}

.admin-current-image-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.admin-current-image-content {
    padding: 20px;
}

.current-image-display {
    text-align: center;
    margin-bottom: 15px;
}

.current-image-display img {
    max-width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid #eee;
}

.current-image-info p {
    margin: 5px 0;
    font-size: 14px;
    color: var(--paraColor);
}

.team-member-preview {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.preview-image {
    width: 120px;
    height: 120px;
    margin: 0 auto 15px;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid var(--primaryColor);
}

.preview-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.preview-info h5 {
    margin: 0 0 5px 0;
    color: var(--headingColor);
    font-size: 18px;
}

.preview-info p {
    margin: 0 0 15px 0;
    color: var(--paraColor);
    font-size: 14px;
}

.preview-social {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.preview-social a {
    width: 35px;
    height: 35px;
    background: var(--primaryColor);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
}

.preview-social a:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}
</style>

<script>
// Initialize preview with current image
document.addEventListener('DOMContentLoaded', function() {
    // Hide file label initially since we have current image
    document.getElementById('fileLabel').style.display = 'none';
});

// Image upload preview
document.getElementById('image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('previewImg').src = e.target.result;
            document.getElementById('preview-member-image').src = e.target.result;
            document.getElementById('imagePreview').style.display = 'block';
            document.getElementById('fileLabel').style.display = 'none';
        };
        reader.readAsDataURL(file);
    }
});

function removeImage() {
    document.getElementById('image').value = '';
    document.getElementById('imagePreview').style.display = 'none';
    document.getElementById('fileLabel').style.display = 'flex';
    // Reset to original image
    document.getElementById('preview-member-image').src = '{{ asset('storage/' . $teamMember->image) }}';
}

// Live preview functionality
document.getElementById('name').addEventListener('input', function(e) {
    document.getElementById('preview-name').textContent = e.target.value || '{{ $teamMember->name }}';
});

document.getElementById('position').addEventListener('input', function(e) {
    document.getElementById('preview-position').textContent = e.target.value || '{{ $teamMember->position }}';
});

// Social media preview
const socialInputs = ['facebook_url', 'linkedin_url', 'twitter_url', 'instagram_url'];
socialInputs.forEach(function(inputId) {
    document.getElementById(inputId).addEventListener('input', function(e) {
        const previewId = 'preview-' + inputId.replace('_url', '');
        const previewElement = document.getElementById(previewId);
        if (e.target.value) {
            previewElement.style.display = 'flex';
            previewElement.href = e.target.value;
        } else {
            previewElement.style.display = 'none';
        }
    });
});

// Drag and drop functionality
const fileUpload = document.querySelector('.admin-file-upload');
const fileInput = document.getElementById('image');

['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
    fileUpload.addEventListener(eventName, preventDefaults, false);
});

function preventDefaults(e) {
    e.preventDefault();
    e.stopPropagation();
}

['dragenter', 'dragover'].forEach(eventName => {
    fileUpload.addEventListener(eventName, highlight, false);
});

['dragleave', 'drop'].forEach(eventName => {
    fileUpload.addEventListener(eventName, unhighlight, false);
});

function highlight(e) {
    fileUpload.classList.add('drag-over');
}

function unhighlight(e) {
    fileUpload.classList.remove('drag-over');
}

fileUpload.addEventListener('drop', handleDrop, false);

function handleDrop(e) {
    const dt = e.dataTransfer;
    const files = dt.files;

    if (files.length > 0) {
        fileInput.files = files;
        fileInput.dispatchEvent(new Event('change'));
    }
}
</script>
@endsection
