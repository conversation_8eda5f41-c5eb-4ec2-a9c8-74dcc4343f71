<!DOCTYPE html>
<html lang="en">

@include('visitors.includes.header')
<body>

    <div class="" id="preloader">
        <div class="img d-none">
            <img src="{{ asset('uploads/website-images/Spinner.gif') }}" alt="UniFood" class="img-fluid">
        </div>
    </div>

    <!--=============================
        ADMIN TOPBAR START
    ==============================-->
    @include('layouts.admin-topbar')
    <!--=============================
        ADMIN TOPBAR END
    ==============================-->

    <!--=============================
        ADMIN SIDEBAR NAVIGATION START
    ==============================-->
    @include('layouts.admin-sidebar')
    <!--=============================
        ADMIN SIDEBAR NAVIGATION END
    ==============================-->

    <!--=============================
        ADMIN DASHBOARD WRAPPER START
    ==============================-->
    <div class="admin-dashboard-wrapper">
        <!-- Mobile Menu Toggle -->
        <div class="admin-mobile-toggle d-lg-none">
            <button class="admin-sidebar-toggle" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <!--=============================
            ADMIN DASHBOARD CONTENT START
        ==============================-->
        <main class="admin-dashboard-content">
            @if(isset($header))
                <section class="admin-header-section">
                    <div class="container-fluid">
                        <div class="row">
                            <div class="col-12">
                                <div class="admin-page-header">
                                    {{ $header }}
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            @endif

            <section class="admin-main-content">
                <div class="container-fluid">
                    @yield('content')
                </div>
            </section>
        </main>
        <!--=============================
            ADMIN DASHBOARD CONTENT END
        ==============================-->

        <h1>Admin Dashboard</h1>
    </div>
    <!--=============================
        ADMIN DASHBOARD WRAPPER END
    ==============================-->

    <!--=============================
        FOOTER START
    ==============================-->
    @include('visitors.includes.footer')
    <!--=============================
        FOOTER END
    ==============================-->

    <!--=============================
        SCROLL BUTTON START
    ==============================-->
    <div class="wsus__scroll_btn">
        Go to top
    </div>
    <!--=============================
        SCROLL BUTTON END
    ==============================-->

    <!--bootstrap js-->
    <script src="{{ asset('user/js/bootstrap.bundle.min.js') }}"></script>
    <!--font-awesome js-->
    <script src="{{ asset('user/js/Font-Awesome.js') }}"></script>
    <!-- slick slider -->
    <script src="{{ asset('user/js/slick.min.js') }}"></script>
    <!-- isotop js -->
    <script src="{{ asset('user/js/isotope.pkgd.min.js') }}"></script>
    <!-- simplyCountdownjs -->
    <script src="{{ asset('user/js/simplyCountdown.js') }}"></script>
    <!-- counter up js -->
    <script src="{{ asset('user/js/jquery.waypoints.min.js') }}"></script>
    <script src="{{ asset('user/js/jquery.countup.min.js') }}"></script>
    <!-- nice select js -->
    <script src="{{ asset('user/js/jquery.nice-select.min.js') }}"></script>
    <!-- venobox js -->
    <script src="{{ asset('user/js/venobox.min.js') }}"></script>
    <!-- sticky sidebar js -->
    <script src="{{ asset('user/js/sticky_sidebar.js') }}"></script>
    <!-- wow js -->
    <script src="{{ asset('user/js/wow.min.js') }}"></script>
    <!-- ex zoom js -->
    <script src="{{ asset('user/js/jquery.exzoom.js') }}"></script>

    <script src="{{ asset('backend/js/bootstrap-datepicker.min.js') }}"></script>

    <!--main/custom js-->
    <script src="{{ asset('user/js/main.js') }}"></script>

    <script src="{{ asset('toastr/toastr.min.js') }}"></script>
    <script src="{{ asset('backend/js/select2.min.js') }}"></script>

    <script>
        (function($) {
            "use strict";
            $(document).ready(function () {
                // Admin dashboard specific JavaScript
                $('.select2').select2();

                $('.datepicker').datepicker({
                    format: 'yyyy-mm-dd',
                    startDate: '-Infinity'
                });

                // Admin dashboard animations
                $('.admin-stat-card').hover(function() {
                    $(this).addClass('admin-card-hover');
                }, function() {
                    $(this).removeClass('admin-card-hover');
                });

                // Sidebar toggle functionality
                $('#sidebarToggle').on('click', function() {
                    $('#adminSidebar').addClass('active');
                    $('#sidebarOverlay').addClass('active');
                });

                $('#sidebarClose, #sidebarOverlay').on('click', function() {
                    $('#adminSidebar').removeClass('active');
                    $('#sidebarOverlay').removeClass('active');
                });

                // Sidebar dropdown toggle
                $('.admin-nav-toggle').on('click', function(e) {
                    e.preventDefault();
                    var $parent = $(this).parent();
                    var $submenu = $parent.find('.admin-nav-submenu');

                    // Close other open dropdowns
                    $('.admin-nav-dropdown').not($parent).removeClass('active');

                    // Toggle current dropdown
                    $parent.toggleClass('active');
                });

                // Close sidebar on window resize if mobile
                $(window).on('resize', function() {
                    if ($(window).width() >= 992) {
                        $('#adminSidebar').removeClass('active');
                        $('#sidebarOverlay').removeClass('active');
                    }
                });

                // Set active navigation based on current URL
                var currentUrl = window.location.pathname;
                $('.admin-nav-link, .admin-nav-sublink').each(function() {
                    var linkUrl = $(this).attr('href');
                    if (linkUrl && currentUrl.includes(linkUrl) && linkUrl !== '#') {
                        $(this).addClass('active');
                        // If it's a submenu item, open the parent dropdown
                        if ($(this).hasClass('admin-nav-sublink')) {
                            $(this).closest('.admin-nav-dropdown').addClass('active');
                        }
                    }
                });
            });
        })(jQuery);
    </script>

    @stack('scripts')

</body>
</html>
