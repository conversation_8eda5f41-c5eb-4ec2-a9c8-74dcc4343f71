<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SectionSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class SectionSettingController extends Controller
{
    public function index()
    {
        $sections = SectionSetting::all();
        return view('admin.content.section-settings.index', compact('sections'));
    }

    public function edit($sectionName)
    {
        $section = SectionSetting::where('section_name', $sectionName)->first();
        if (!$section) {
            $section = new SectionSetting(['section_name' => $sectionName]);
        }
        return view('admin.content.section-settings.edit', compact('section'));
    }

    public function update(Request $request, $sectionName)
    {
        $request->validate([
            'title' => 'nullable|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'background_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'left_shape_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'right_shape_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean'
        ]);

        $section = SectionSetting::where('section_name', $sectionName)->first();
        $data = $request->all();
        $data['section_name'] = $sectionName;
        
        if ($request->hasFile('background_image')) {
            if ($section && $section->background_image && Storage::disk('public')->exists($section->background_image)) {
                Storage::disk('public')->delete($section->background_image);
            }
            $data['background_image'] = $request->file('background_image')->store('section-settings', 'public');
        }
        
        if ($request->hasFile('left_shape_image')) {
            if ($section && $section->left_shape_image && Storage::disk('public')->exists($section->left_shape_image)) {
                Storage::disk('public')->delete($section->left_shape_image);
            }
            $data['left_shape_image'] = $request->file('left_shape_image')->store('section-settings', 'public');
        }
        
        if ($request->hasFile('right_shape_image')) {
            if ($section && $section->right_shape_image && Storage::disk('public')->exists($section->right_shape_image)) {
                Storage::disk('public')->delete($section->right_shape_image);
            }
            $data['right_shape_image'] = $request->file('right_shape_image')->store('section-settings', 'public');
        }

        if ($section) {
            $section->update($data);
        } else {
            SectionSetting::create($data);
        }

        return redirect()->route('admin.section-settings.index')
            ->with('success', 'Section settings updated successfully.');
    }

    public function toggleStatus($sectionName)
    {
        $section = SectionSetting::where('section_name', $sectionName)->first();
        if ($section) {
            $section->update(['is_active' => !$section->is_active]);
            
            return response()->json([
                'success' => true,
                'message' => 'Status updated successfully.',
                'is_active' => $section->is_active
            ]);
        }
        
        return response()->json([
            'success' => false,
            'message' => 'Section not found.'
        ]);
    }
}
