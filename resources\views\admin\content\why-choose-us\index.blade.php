@extends('admin.admin-layout')

@section('content')
<div class="row">
    <!-- Creative Header -->
    <div class="col-12">
        <div class="admin-creative-header mb-4">
            <div class="admin-header-content">
                <div class="admin-header-main">
                    <div class="admin-header-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="admin-header-text">
                        <h1 class="admin-header-title">Why Choose Us</h1>
                        <p class="admin-header-subtitle">Manage your key features and benefits section</p>
                    </div>
                </div>
                <div class="admin-header-actions">
                    <a href="{{ route('admin.why-choose-us.create') }}" class="admin-btn">
                        <i class="fas fa-plus"></i> Add New Feature
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="admin-table-wrapper">
            <div class="admin-table-header">
                <h3 class="admin-table-title">All Features</h3>
                <div class="admin-table-actions">
                    <span class="admin-badge admin-badge-info">{{ $features->count() }} Total</span>
                </div>
            </div>
            
            @if($features->count() > 0)
                <div class="row">
                    @foreach($features as $feature)
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="admin-feature-card">
                            <div class="admin-feature-icon">
                                <i class="{{ $feature->icon }}"></i>
                                <div class="admin-feature-status">
                                    <div class="admin-toggle-switch">
                                        <input type="checkbox" id="status-{{ $feature->id }}" 
                                               {{ $feature->is_active ? 'checked' : '' }}
                                               onchange="toggleStatus({{ $feature->id }}, '{{ route('admin.why-choose-us.toggle-status', $feature) }}')">
                                        <label for="status-{{ $feature->id }}"></label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="admin-feature-content">
                                <h4 class="admin-feature-title">{{ $feature->title }}</h4>
                                <p class="admin-feature-description">{{ Str::limit($feature->description, 100) }}</p>
                                
                                <div class="admin-feature-meta">
                                    <span class="admin-badge admin-badge-secondary">Order: {{ $feature->sort_order }}</span>
                                    <span class="admin-badge admin-badge-primary">
                                        <i class="{{ $feature->icon }}"></i> Icon
                                    </span>
                                </div>
                                
                                <div class="admin-feature-actions">
                                    <a href="{{ route('admin.why-choose-us.show', $feature) }}" class="admin-btn admin-btn-sm admin-btn-info">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    <a href="{{ route('admin.why-choose-us.edit', $feature) }}" class="admin-btn admin-btn-sm admin-btn-warning">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    <form action="{{ route('admin.why-choose-us.destroy', $feature) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="admin-btn admin-btn-sm admin-btn-danger" 
                                                onclick="return confirm('Are you sure you want to delete this feature?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            @else
                <div class="admin-empty-state">
                    <div class="admin-empty-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3>No Features Found</h3>
                    <p>Add your first feature to highlight why customers should choose your restaurant.</p>
                    <a href="{{ route('admin.why-choose-us.create') }}" class="admin-btn">
                        <i class="fas fa-plus"></i> Add First Feature
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<style>
.admin-feature-card {
    background: var(--cardBg);
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    text-align: center;
}

.admin-feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.admin-feature-icon {
    position: relative;
    margin-bottom: 20px;
}

.admin-feature-icon i {
    font-size: 48px;
    color: var(--primaryColor);
    background: linear-gradient(135deg, var(--primaryColor), #ff6b6b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.admin-feature-status {
    position: absolute;
    top: -10px;
    right: -10px;
}

.admin-feature-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--headingColor);
    margin-bottom: 10px;
}

.admin-feature-description {
    color: var(--paraColor);
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 15px;
}

.admin-feature-meta {
    margin-bottom: 20px;
}

.admin-feature-meta .admin-badge {
    margin-right: 8px;
    margin-bottom: 5px;
}

.admin-feature-actions {
    display: flex;
    gap: 8px;
}

.admin-feature-actions .admin-btn {
    flex: 1;
    text-align: center;
}
</style>

<script>
function toggleStatus(id, url) {
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}
</script>
@endsection
