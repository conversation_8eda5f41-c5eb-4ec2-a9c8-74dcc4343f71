<?php

namespace Database\Seeders;

use App\Models\BrandPartner;
use Illuminate\Database\Seeder;

class BrandPartnerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $partners = [
            [
                'name' => 'Partner 1',
                'logo' => 'uploads/custom-images/partner-2023-03-05-04-35-57-4275.png',
                'url' => 'javascript:;',
                'is_active' => true,
                'sort_order' => 1
            ],
            [
                'name' => 'Partner 2',
                'logo' => 'uploads/custom-images/partner-2023-03-05-04-36-05-2512.png',
                'url' => 'javascript:;',
                'is_active' => true,
                'sort_order' => 2
            ],
            [
                'name' => 'Partner 3',
                'logo' => 'uploads/custom-images/partner-2023-03-05-04-36-15-7213.png',
                'url' => 'javascript:;',
                'is_active' => true,
                'sort_order' => 3
            ],
            [
                'name' => 'Partner 4',
                'logo' => 'uploads/custom-images/partner-2023-03-05-04-36-24-3552.png',
                'url' => 'javascript:;',
                'is_active' => true,
                'sort_order' => 4
            ],
            [
                'name' => 'Partner 5',
                'logo' => 'uploads/custom-images/partner-2023-03-05-04-36-34-1671.png',
                'url' => 'javascript:;',
                'is_active' => true,
                'sort_order' => 5
            ],
            [
                'name' => 'Partner 6',
                'logo' => 'uploads/custom-images/partner-2023-03-05-04-36-42-1713.png',
                'url' => 'javascript:;',
                'is_active' => true,
                'sort_order' => 6
            ]
        ];

        foreach ($partners as $partner) {
            BrandPartner::create($partner);
        }
    }
}
