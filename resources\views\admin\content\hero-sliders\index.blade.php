@extends('admin.admin-layout')

@section('content')
<div class="row">
    <!-- Creative Header -->
    <div class="col-12">
        <div class="admin-creative-header mb-4">
            <div class="admin-header-content">
                <div class="admin-header-main">
                    <div class="admin-header-icon">
                        <i class="fas fa-images"></i>
                    </div>
                    <div class="admin-header-text">
                        <h1 class="admin-header-title">Hero Sliders</h1>
                        <p class="admin-header-subtitle">Manage your homepage hero slider content</p>
                    </div>
                </div>
                <div class="admin-header-actions">
                    <a href="{{ route('admin.hero-sliders.create') }}" class="admin-btn">
                        <i class="fas fa-plus"></i> Add New Slider
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="admin-table-wrapper">
            <div class="admin-table-header">
                <h3 class="admin-table-title">All Hero Sliders</h3>
                <div class="admin-table-actions">
                    <span class="admin-badge admin-badge-info">{{ $sliders->count() }} Total</span>
                </div>
            </div>
            
            @if($sliders->count() > 0)
                <div class="table-responsive">
                    <table class="table admin-table">
                        <thead>
                            <tr>
                                <th>Image</th>
                                <th>Title</th>
                                <th>Subtitle</th>
                                <th>Offer</th>
                                <th>Order</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($sliders as $slider)
                            <tr>
                                <td>
                                    <div class="admin-table-image">
                                        <img src="{{ asset('storage/' . $slider->image) }}" alt="{{ $slider->title }}" class="img-fluid">
                                    </div>
                                </td>
                                <td>
                                    <div class="admin-table-text">
                                        <strong>{{ $slider->title }}</strong>
                                    </div>
                                </td>
                                <td>{{ $slider->subtitle }}</td>
                                <td>
                                    @if($slider->offer_text)
                                        <span class="admin-badge admin-badge-warning">{{ $slider->offer_text }}</span>
                                    @else
                                        <span class="text-muted">No offer</span>
                                    @endif
                                </td>
                                <td>
                                    <span class="admin-badge admin-badge-secondary">{{ $slider->sort_order }}</span>
                                </td>
                                <td>
                                    <div class="admin-toggle-switch">
                                        <input type="checkbox" id="status-{{ $slider->id }}" 
                                               {{ $slider->is_active ? 'checked' : '' }}
                                               onchange="toggleStatus({{ $slider->id }}, '{{ route('admin.hero-sliders.toggle-status', $slider) }}')">
                                        <label for="status-{{ $slider->id }}"></label>
                                    </div>
                                </td>
                                <td>
                                    <div class="admin-table-actions">
                                        <a href="{{ route('admin.hero-sliders.show', $slider) }}" class="admin-btn admin-btn-sm admin-btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.hero-sliders.edit', $slider) }}" class="admin-btn admin-btn-sm admin-btn-warning">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ route('admin.hero-sliders.destroy', $slider) }}" method="POST" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="admin-btn admin-btn-sm admin-btn-danger" 
                                                    onclick="return confirm('Are you sure you want to delete this slider?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="admin-empty-state">
                    <div class="admin-empty-icon">
                        <i class="fas fa-images"></i>
                    </div>
                    <h3>No Hero Sliders Found</h3>
                    <p>Create your first hero slider to get started with your homepage content.</p>
                    <a href="{{ route('admin.hero-sliders.create') }}" class="admin-btn">
                        <i class="fas fa-plus"></i> Create First Slider
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<script>
function toggleStatus(id, url) {
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            console.log(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}
</script>
@endsection
