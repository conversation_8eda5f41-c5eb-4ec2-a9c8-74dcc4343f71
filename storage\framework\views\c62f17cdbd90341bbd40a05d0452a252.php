<?php $__env->startSection('content'); ?>
<div class="row">
    <!-- Enhanced Creative Header -->
    <div class="col-12">
        <div class="admin-creative-header mb-4">
            <div class="admin-header-content">
                <div class="admin-header-main">
                    <div class="admin-header-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="admin-header-text">
                        <h1 class="admin-header-title"><?php echo e(__('Dashboard')); ?></h1>
                        <p class="admin-header-subtitle">Welcome back, <?php echo e(Auth::user()->name); ?>! Here's what's happening today.</p>
                    </div>
                </div>
                <div class="admin-header-actions">
                    <div class="admin-header-time">
                        <i class="fas fa-clock"></i>
                        <span><?php echo e(now()->format('H:i A')); ?></span>
                    </div>
                    <div class="admin-header-date">
                        <i class="fas fa-calendar"></i>
                        <span><?php echo e(now()->format('M d, Y')); ?></span>
                    </div>
                </div>
            </div>
            <nav class="admin-breadcrumb">
                <span class="admin-breadcrumb-item active">
                    <i class="fas fa-home"></i> Dashboard
                </span>
            </nav>
        </div>
    </div>
</div>

<div class="row">
    <!-- Statistics Cards -->
    <div class="col-xl-3 col-md-6 col-sm-6">
        <div class="admin-stat-card">
            <div class="admin-stat-icon">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <div class="admin-stat-number">1,234</div>
            <p class="admin-stat-label">Total Orders</p>
            <div class="admin-stat-change positive">
                <i class="fas fa-arrow-up"></i> +12% from last month
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 col-sm-6">
        <div class="admin-stat-card">
            <div class="admin-stat-icon">
                <i class="fas fa-dollar-sign"></i>
            </div>
            <div class="admin-stat-number">$45,678</div>
            <p class="admin-stat-label">Total Revenue</p>
            <div class="admin-stat-change positive">
                <i class="fas fa-arrow-up"></i> +8% from last month
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 col-sm-6">
        <div class="admin-stat-card">
            <div class="admin-stat-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="admin-stat-number">567</div>
            <p class="admin-stat-label">Total Customers</p>
            <div class="admin-stat-change positive">
                <i class="fas fa-arrow-up"></i> +15% from last month
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 col-sm-6">
        <div class="admin-stat-card">
            <div class="admin-stat-icon">
                <i class="fas fa-utensils"></i>
            </div>
            <div class="admin-stat-number">89</div>
            <p class="admin-stat-label">Menu Items</p>
            <div class="admin-stat-change negative">
                <i class="fas fa-arrow-down"></i> -2% from last month
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Orders Table -->
    <div class="col-xl-8 col-lg-7">
        <div class="admin-table-wrapper">
            <div class="admin-table-header">
                <h3 class="admin-table-title">Recent Orders</h3>
                <a href="#" class="admin-btn admin-btn-sm">View All</a>
            </div>
            <table class="admin-table">
                <thead>
                    <tr>
                        <th>Order ID</th>
                        <th>Customer</th>
                        <th>Amount</th>
                        <th>Status</th>
                        <th>Date</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>#ORD-001</td>
                        <td>John Doe</td>
                        <td>$45.99</td>
                        <td><span class="admin-btn admin-btn-success admin-btn-sm">Completed</span></td>
                        <td>2024-01-15</td>
                        <td>
                            <a href="#" class="admin-btn admin-btn-secondary admin-btn-sm">View</a>
                        </td>
                    </tr>
                    <tr>
                        <td>#ORD-002</td>
                        <td>Jane Smith</td>
                        <td>$32.50</td>
                        <td><span class="admin-btn admin-btn-sm" style="background: #ffc107; color: #000;">Pending</span></td>
                        <td>2024-01-15</td>
                        <td>
                            <a href="#" class="admin-btn admin-btn-secondary admin-btn-sm">View</a>
                        </td>
                    </tr>
                    <tr>
                        <td>#ORD-003</td>
                        <td>Mike Johnson</td>
                        <td>$78.25</td>
                        <td><span class="admin-btn admin-btn-success admin-btn-sm">Completed</span></td>
                        <td>2024-01-14</td>
                        <td>
                            <a href="#" class="admin-btn admin-btn-secondary admin-btn-sm">View</a>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="col-xl-4 col-lg-5">
        <div class="admin-table-wrapper">
            <div class="admin-table-header">
                <h3 class="admin-table-title">Quick Actions</h3>
            </div>
            <div class="d-grid gap-3">
                <a href="#" class="admin-btn">
                    <i class="fas fa-plus-circle"></i> Add New Product
                </a>
                <a href="#" class="admin-btn admin-btn-secondary">
                    <i class="fas fa-list"></i> Manage Orders
                </a>
                <a href="#" class="admin-btn admin-btn-success">
                    <i class="fas fa-users"></i> View Customers
                </a>
                <a href="#" class="admin-btn" style="background: #17a2b8;">
                    <i class="fas fa-chart-bar"></i> View Reports
                </a>
            </div>
        </div>

        <!-- Welcome Message -->
        <div class="admin-table-wrapper">
            <div class="admin-table-header">
                <h3 class="admin-table-title">Welcome Back!</h3>
            </div>
            <p style="color: var(--paraColor); margin: 0;">
                Hello <strong><?php echo e(Auth::user()->name); ?></strong>! You're successfully logged into the admin dashboard.
                Here you can manage your restaurant's orders, menu items, customers, and view detailed reports.
            </p>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.admin-layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Laravel-Apps\gfood\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>