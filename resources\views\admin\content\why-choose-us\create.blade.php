@extends('admin.admin-layout')

@section('content')
<div class="row">
    <!-- Creative Header -->
    <div class="col-12">
        <div class="admin-creative-header mb-4">
            <div class="admin-header-content">
                <div class="admin-header-main">
                    <div class="admin-header-icon">
                        <i class="fas fa-plus-circle"></i>
                    </div>
                    <div class="admin-header-text">
                        <h1 class="admin-header-title">Create Feature</h1>
                        <p class="admin-header-subtitle">Add a new feature to your "Why Choose Us" section</p>
                    </div>
                </div>
                <div class="admin-header-actions">
                    <a href="{{ route('admin.why-choose-us.index') }}" class="admin-btn admin-btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="admin-form-wrapper">
            <div class="admin-form-header">
                <h3 class="admin-form-title">Feature Information</h3>
            </div>
            
            <form action="{{ route('admin.why-choose-us.store') }}" method="POST">
                @csrf
                
                <div class="admin-form-group">
                    <label for="title" class="admin-form-label">Feature Title *</label>
                    <input type="text" class="admin-form-control @error('title') is-invalid @enderror" 
                           id="title" name="title" value="{{ old('title') }}" required>
                    @error('title')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="admin-form-group">
                    <label for="description" class="admin-form-label">Description *</label>
                    <textarea class="admin-form-control @error('description') is-invalid @enderror" 
                              id="description" name="description" rows="4" required>{{ old('description') }}</textarea>
                    @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="admin-form-group">
                            <label for="icon" class="admin-form-label">FontAwesome Icon Class *</label>
                            <input type="text" class="admin-form-control @error('icon') is-invalid @enderror" 
                                   id="icon" name="icon" value="{{ old('icon') }}" 
                                   placeholder="e.g., fas fa-check-circle" required>
                            @error('icon')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">
                                Visit <a href="https://fontawesome.com/icons" target="_blank">FontAwesome</a> for icon classes
                            </small>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="admin-form-group">
                            <label for="sort_order" class="admin-form-label">Sort Order</label>
                            <input type="number" class="admin-form-control @error('sort_order') is-invalid @enderror" 
                                   id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" min="0">
                            @error('sort_order')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
                
                <div class="admin-form-actions">
                    <button type="submit" class="admin-btn admin-btn-primary">
                        <i class="fas fa-save"></i> Create Feature
                    </button>
                    <a href="{{ route('admin.why-choose-us.index') }}" class="admin-btn admin-btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="admin-info-card">
            <div class="admin-info-header">
                <h4><i class="fas fa-info-circle"></i> Feature Guidelines</h4>
            </div>
            <div class="admin-info-content">
                <ul class="admin-info-list">
                    <li><strong>Title:</strong> Keep it short and impactful</li>
                    <li><strong>Description:</strong> Brief explanation of the benefit</li>
                    <li><strong>Icon:</strong> Use FontAwesome icon classes</li>
                    <li><strong>Sort Order:</strong> Lower numbers appear first</li>
                </ul>
            </div>
        </div>
        
        <div class="admin-preview-card">
            <div class="admin-preview-header">
                <h4><i class="fas fa-eye"></i> Live Preview</h4>
            </div>
            <div class="admin-preview-content">
                <div class="feature-preview">
                    <div class="preview-icon">
                        <i id="preview-icon" class="fas fa-check-circle"></i>
                    </div>
                    <div class="preview-text">
                        <h5 id="preview-title">Feature Title</h5>
                        <p id="preview-description">Feature description will appear here...</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="admin-status-card">
            <div class="admin-status-header">
                <h4><i class="fas fa-toggle-on"></i> Status Settings</h4>
            </div>
            <div class="admin-status-content">
                <div class="admin-form-group">
                    <div class="admin-checkbox-wrapper">
                        <input type="checkbox" id="is_active" name="is_active" value="1" 
                               {{ old('is_active', true) ? 'checked' : '' }}>
                        <label for="is_active" class="admin-checkbox-label">
                            <span class="admin-checkbox-text">Active</span>
                            <small class="admin-checkbox-help">Enable this feature to show on homepage</small>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.feature-preview {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.preview-icon i {
    font-size: 32px;
    color: var(--primaryColor);
}

.preview-text h5 {
    margin: 0 0 5px 0;
    color: var(--headingColor);
}

.preview-text p {
    margin: 0;
    color: var(--paraColor);
    font-size: 14px;
}
</style>

<script>
// Live preview functionality
document.getElementById('title').addEventListener('input', function(e) {
    document.getElementById('preview-title').textContent = e.target.value || 'Feature Title';
});

document.getElementById('description').addEventListener('input', function(e) {
    document.getElementById('preview-description').textContent = e.target.value || 'Feature description will appear here...';
});

document.getElementById('icon').addEventListener('input', function(e) {
    const iconElement = document.getElementById('preview-icon');
    iconElement.className = e.target.value || 'fas fa-check-circle';
});
</script>
@endsection
