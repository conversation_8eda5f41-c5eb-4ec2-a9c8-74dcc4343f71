<!DOCTYPE html>
<html lang="en">

<?php echo $__env->make('admin.includes.header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<body>

    <div class="" id="preloader">
        <div class="img d-none">
            <img src="<?php echo e(asset('uploads/website-images/Spinner.gif')); ?>" alt="UniFood" class="img-fluid">
        </div>
    </div>

    <!--=============================
        ADMIN TOPBAR START
    ==============================-->
    <?php echo $__env->make('admin.includes.admin-topbar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <!--=============================
        ADMIN TOPBAR END
    ==============================-->

    <!--=============================
        ADMIN SIDEBAR NAVIGATION START
    ==============================-->
    <?php echo $__env->make('admin.includes.admin-sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <!--==========================
        ADMIN SIDEBAR NAVIGATION END
    ==============================-->

    <!-- Sidebar Overlay for Mobile -->
    <div class="admin-sidebar-overlay" id="sidebarOverlay"></div>

    <!--=============================
        ADMIN DASHBOARD WRAPPER START
    ==============================-->
    <div class="admin-dashboard-wrapper">
        <!--=============================
            ADMIN DASHBOARD CONTENT START
        ==============================-->
        <main class="admin-dashboard-content">
            <?php if(isset($header)): ?>
                <section class="admin-header-section">
                    <div class="container-fluid">
                        <div class="row">
                            <div class="col-12">
                                <div class="admin-page-header">
                                    <?php echo e($header); ?>

                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            <?php endif; ?>

            <section class="admin-main-content">
                <div class="container-fluid">
                    <?php echo $__env->yieldContent('content'); ?>
                </div>
            </section>
        </main>
        <!--=============================
            ADMIN DASHBOARD CONTENT END
        ==============================-->

        <!--=============================
            FOOTER START
        ==============================-->
        <?php echo $__env->make('visitors.includes.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <!--=============================
            FOOTER END
        ==============================-->
    </div>
    <!--=============================
        ADMIN DASHBOARD WRAPPER END
    ==============================-->



    <!--=============================
        SCROLL BUTTON START
    ==============================-->
    <div class="wsus__scroll_btn">
        Go to top
    </div>
    <!--=============================
        SCROLL BUTTON END
    ==============================-->

    <!--bootstrap js-->
    <script src="<?php echo e(asset('user/js/bootstrap.bundle.min.js')); ?>"></script>
    <!--font-awesome js-->
    <script src="<?php echo e(asset('user/js/Font-Awesome.js')); ?>"></script>
    <!-- slick slider -->
    <script src="<?php echo e(asset('user/js/slick.min.js')); ?>"></script>
    <!-- isotop js -->
    <script src="<?php echo e(asset('user/js/isotope.pkgd.min.js')); ?>"></script>
    <!-- simplyCountdownjs -->
    <script src="<?php echo e(asset('user/js/simplyCountdown.js')); ?>"></script>
    <!-- counter up js -->
    <script src="<?php echo e(asset('user/js/jquery.waypoints.min.js')); ?>"></script>
    <script src="<?php echo e(asset('user/js/jquery.countup.min.js')); ?>"></script>
    <!-- nice select js -->
    <script src="<?php echo e(asset('user/js/jquery.nice-select.min.js')); ?>"></script>
    <!-- venobox js -->
    <script src="<?php echo e(asset('user/js/venobox.min.js')); ?>"></script>
    <!-- sticky sidebar js -->
    <script src="<?php echo e(asset('user/js/sticky_sidebar.js')); ?>"></script>
    <!-- wow js -->
    <script src="<?php echo e(asset('user/js/wow.min.js')); ?>"></script>
    <!-- ex zoom js -->
    <script src="<?php echo e(asset('user/js/jquery.exzoom.js')); ?>"></script>

    <script src="<?php echo e(asset('backend/js/bootstrap-datepicker.min.js')); ?>"></script>

    <!--main/custom js-->
    <script src="<?php echo e(asset('user/js/main.js')); ?>"></script>

    <script src="<?php echo e(asset('toastr/toastr.min.js')); ?>"></script>
    <script src="<?php echo e(asset('backend/js/select2.min.js')); ?>"></script>

    <script>
        (function($) {
            "use strict";
            $(document).ready(function () {
                // Admin dashboard specific JavaScript
                if (typeof $.fn.select2 !== 'undefined') {
                    $('.select2').select2();
                }

                if (typeof $.fn.datepicker !== 'undefined') {
                    $('.datepicker').datepicker({
                        format: 'yyyy-mm-dd',
                        startDate: '-Infinity'
                    });
                }

                // Admin dashboard animations
                $('.admin-stat-card').hover(function() {
                    $(this).addClass('admin-card-hover');
                }, function() {
                    $(this).removeClass('admin-card-hover');
                });

                // Sidebar toggle functionality
                $('#sidebarToggle').on('click', function(e) {
                    e.preventDefault();
                    $('#adminSidebar').addClass('active');
                    $('#sidebarOverlay').addClass('active');
                    $('body').addClass('sidebar-open');
                });

                $('#sidebarClose, #sidebarOverlay').on('click', function(e) {
                    e.preventDefault();
                    $('#adminSidebar').removeClass('active');
                    $('#sidebarOverlay').removeClass('active');
                    $('body').removeClass('sidebar-open');
                });

                // Sidebar dropdown toggle
                $('.admin-nav-toggle').on('click', function(e) {
                    e.preventDefault();
                    var $parent = $(this).parent();
                    var $submenu = $parent.find('.admin-nav-submenu');

                    // Close other open dropdowns
                    $('.admin-nav-dropdown').not($parent).removeClass('active');
                    $('.admin-nav-dropdown').not($parent).find('.admin-nav-submenu').slideUp(300);

                    // Toggle current dropdown
                    $parent.toggleClass('active');

                    if ($parent.hasClass('active')) {
                        $submenu.slideDown(300);
                    } else {
                        $submenu.slideUp(300);
                    }
                });

                // Handle window resize
                $(window).on('resize', function() {
                    if ($(window).width() >= 992) {
                        $('#adminSidebar').removeClass('active');
                        $('#sidebarOverlay').removeClass('active');
                        $('body').removeClass('sidebar-open');
                    }
                });

                // Set active navigation based on current URL
                var currentUrl = window.location.pathname;
                $('.admin-nav-link, .admin-nav-sublink').each(function() {
                    var linkUrl = $(this).attr('href');
                    if (linkUrl && linkUrl !== '#' && (currentUrl === linkUrl || currentUrl.includes(linkUrl))) {
                        $(this).addClass('active');
                        // If it's a submenu item, open the parent dropdown
                        if ($(this).hasClass('admin-nav-sublink')) {
                            var $dropdown = $(this).closest('.admin-nav-dropdown');
                            $dropdown.addClass('active');
                            $dropdown.find('.admin-nav-submenu').show();
                        }
                    }
                });

                // Prevent body scroll when sidebar is open on mobile
                $('#sidebarToggle').on('click', function() {
                    if ($(window).width() < 992) {
                        $('body').css('overflow', 'hidden');
                    }
                });

                $('#sidebarClose, #sidebarOverlay').on('click', function() {
                    $('body').css('overflow', '');
                });
            });
        })(jQuery);
    </script>

    <?php echo $__env->yieldPushContent('scripts'); ?>

</body>
</html>
<?php /**PATH D:\Laravel-Apps\gfood\resources\views/admin/admin-layout.blade.php ENDPATH**/ ?>