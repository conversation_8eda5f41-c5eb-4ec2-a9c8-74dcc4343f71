@extends('admin.admin-layout')

@section('content')
<div class="row">
    <!-- Creative Header -->
    <div class="col-12">
        <div class="admin-creative-header mb-4">
            <div class="admin-header-content">
                <div class="admin-header-main">
                    <div class="admin-header-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="admin-header-text">
                        <h1 class="admin-header-title">Daily Offers</h1>
                        <p class="admin-header-subtitle">Manage your special daily offer items</p>
                    </div>
                </div>
                <div class="admin-header-actions">
                    <a href="{{ route('admin.daily-offers.create') }}" class="admin-btn">
                        <i class="fas fa-plus"></i> Add New Offer
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="admin-table-wrapper">
            <div class="admin-table-header">
                <h3 class="admin-table-title">All Daily Offers</h3>
                <div class="admin-table-actions">
                    <span class="admin-badge admin-badge-info">{{ $offers->count() }} Total</span>
                </div>
            </div>
            
            @if($offers->count() > 0)
                <div class="row">
                    @foreach($offers as $offer)
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="admin-offer-card">
                            <div class="admin-offer-image">
                                <img src="{{ asset('storage/' . $offer->image) }}" alt="{{ $offer->title }}" class="img-fluid">
                                <div class="admin-offer-discount">{{ $offer->discount_percentage }}% OFF</div>
                                <div class="admin-offer-status">
                                    <div class="admin-toggle-switch">
                                        <input type="checkbox" id="status-{{ $offer->id }}" 
                                               {{ $offer->is_active ? 'checked' : '' }}
                                               onchange="toggleStatus({{ $offer->id }}, '{{ route('admin.daily-offers.toggle-status', $offer) }}')">
                                        <label for="status-{{ $offer->id }}"></label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="admin-offer-content">
                                <h4 class="admin-offer-title">{{ $offer->title }}</h4>
                                <p class="admin-offer-description">{{ Str::limit($offer->description, 100) }}</p>
                                
                                <div class="admin-offer-meta">
                                    <span class="admin-badge admin-badge-secondary">Order: {{ $offer->sort_order }}</span>
                                    @if($offer->product_url)
                                        <span class="admin-badge admin-badge-info">Has Link</span>
                                    @endif
                                </div>
                                
                                <div class="admin-offer-actions">
                                    <a href="{{ route('admin.daily-offers.show', $offer) }}" class="admin-btn admin-btn-sm admin-btn-info">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    <a href="{{ route('admin.daily-offers.edit', $offer) }}" class="admin-btn admin-btn-sm admin-btn-warning">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    <form action="{{ route('admin.daily-offers.destroy', $offer) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="admin-btn admin-btn-sm admin-btn-danger" 
                                                onclick="return confirm('Are you sure you want to delete this offer?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            @else
                <div class="admin-empty-state">
                    <div class="admin-empty-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <h3>No Daily Offers Found</h3>
                    <p>Create your first daily offer to showcase special deals to your customers.</p>
                    <a href="{{ route('admin.daily-offers.create') }}" class="admin-btn">
                        <i class="fas fa-plus"></i> Create First Offer
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<style>
.admin-offer-card {
    background: var(--cardBg);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
}

.admin-offer-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.admin-offer-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.admin-offer-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.admin-offer-discount {
    position: absolute;
    top: 15px;
    left: 15px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
}

.admin-offer-status {
    position: absolute;
    top: 15px;
    right: 15px;
}

.admin-offer-content {
    padding: 20px;
}

.admin-offer-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--headingColor);
    margin-bottom: 10px;
}

.admin-offer-description {
    color: var(--paraColor);
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 15px;
}

.admin-offer-meta {
    margin-bottom: 15px;
}

.admin-offer-meta .admin-badge {
    margin-right: 8px;
}

.admin-offer-actions {
    display: flex;
    gap: 8px;
}

.admin-offer-actions .admin-btn {
    flex: 1;
    text-align: center;
}
</style>

<script>
function toggleStatus(id, url) {
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}
</script>
@endsection
