<?php

namespace Database\Seeders;

use App\Models\Counter;
use Illuminate\Database\Seeder;

class CounterSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $counters = [
            [
                'title' => 'Customer Serve',
                'count' => 1200,
                'icon' => 'fas fa-burger-soda',
                'background_image' => 'uploads/website-images/counter-bg-2023-03-06-09-34-03-6121.jpg',
                'is_active' => true,
                'sort_order' => 1
            ],
            [
                'title' => 'Experience Chef',
                'count' => 1150,
                'icon' => 'fal fa-hat-chef',
                'background_image' => 'uploads/website-images/counter-bg-2023-03-06-09-34-03-6121.jpg',
                'is_active' => true,
                'sort_order' => 2
            ],
            [
                'title' => 'Happy Customer',
                'count' => 1250,
                'icon' => 'far fa-handshake',
                'background_image' => 'uploads/website-images/counter-bg-2023-03-06-09-34-03-6121.jpg',
                'is_active' => true,
                'sort_order' => 3
            ],
            [
                'title' => 'Winning Award',
                'count' => 1300,
                'icon' => 'far fa-trophy',
                'background_image' => 'uploads/website-images/counter-bg-2023-03-06-09-34-03-6121.jpg',
                'is_active' => true,
                'sort_order' => 4
            ]
        ];

        foreach ($counters as $counter) {
            Counter::create($counter);
        }
    }
}
