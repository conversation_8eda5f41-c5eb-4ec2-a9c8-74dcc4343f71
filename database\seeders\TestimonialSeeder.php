<?php

namespace Database\Seeders;

use App\Models\Testimonial;
use Illuminate\Database\Seeder;

class TestimonialSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $testimonials = [
            [
                'name' => 'Elia Navy',
                'position' => 'Web Developer',
                'image' => 'uploads/custom-images/elia-navy-20230305045641.jpg',
                'feedback' => 'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aut accusamus praesentium quaerat nihil magnam a porro eaque numquam',
                'rating' => 5,
                'product_image' => 'uploads/custom-images/testimonial-product-20230305045641.png',
                'is_active' => true,
                'sort_order' => 1
            ],
            [
                'name' => '<PERSON>',
                'position' => 'MBBS, FCPS, FRCS',
                'image' => 'uploads/custom-images/john-abraham-20230305045819.jpg',
                'feedback' => 'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aut accusamus praesentium quaerat nihil magnam a porro eaque numquam',
                'rating' => 4,
                'product_image' => 'uploads/custom-images/testimonial-product-20230305045819.png',
                'is_active' => true,
                'sort_order' => 2
            ],
            [
                'name' => 'Jose Larry',
                'position' => 'Web Designer',
                'image' => 'uploads/custom-images/jose-larry-20230305050016.jpg',
                'feedback' => 'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aut accusamus praesentium quaerat nihil magnam a porro eaque numquam',
                'rating' => 5,
                'product_image' => 'uploads/custom-images/testimonial-product-20230305050016.png',
                'is_active' => true,
                'sort_order' => 3
            ],
            [
                'name' => 'David Richard',
                'position' => 'Graphic Designer',
                'image' => 'uploads/custom-images/david-richard-20230305050113.jpg',
                'feedback' => 'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aut accusamus praesentium quaerat nihil magnam a porro eaque numquam',
                'rating' => 3,
                'product_image' => 'uploads/custom-images/testimonial-product-20230305050113.png',
                'is_active' => true,
                'sort_order' => 4
            ],
            [
                'name' => 'David Simmons',
                'position' => 'MBBS, FCPS, FRCS',
                'image' => 'uploads/custom-images/david-simmons-20230305050400.jpg',
                'feedback' => 'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aut accusamus praesentium quaerat nihil magnam a porro eaque numquam',
                'rating' => 4,
                'product_image' => 'uploads/custom-images/testimonial-product-20230305050400.png',
                'is_active' => true,
                'sort_order' => 5
            ],
            [
                'name' => 'Mary Patricia',
                'position' => 'Senior Chef',
                'image' => 'uploads/custom-images/mary-patricia-20230305050436.jpg',
                'feedback' => 'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aut accusamus praesentium quaerat nihil magnam a porro eaque numquam',
                'rating' => 5,
                'product_image' => 'uploads/custom-images/testimonial-product-20230305050436.png',
                'is_active' => true,
                'sort_order' => 6
            ]
        ];

        foreach ($testimonials as $testimonial) {
            Testimonial::create($testimonial);
        }
    }
}
