@extends('admin.admin-layout')

@section('content')
<div class="row">
    <!-- Creative Header -->
    <div class="col-12">
        <div class="admin-creative-header mb-4">
            <div class="admin-header-content">
                <div class="admin-header-main">
                    <div class="admin-header-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <div class="admin-header-text">
                        <h1 class="admin-header-title">Section Settings</h1>
                        <p class="admin-header-subtitle">Configure homepage section titles, descriptions, and backgrounds</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="admin-table-wrapper">
            <div class="admin-table-header">
                <h3 class="admin-table-title">Homepage Sections</h3>
                <div class="admin-table-actions">
                    <span class="admin-badge admin-badge-info">{{ $sections->count() }} Sections</span>
                </div>
            </div>
            
            @if($sections->count() > 0)
                <div class="row">
                    @foreach($sections as $section)
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="admin-section-card">
                            <div class="admin-section-header">
                                <div class="admin-section-icon">
                                    @switch($section->section_name)
                                        @case('banner')
                                            <i class="fas fa-images"></i>
                                            @break
                                        @case('daily_offer')
                                            <i class="fas fa-percentage"></i>
                                            @break
                                        @case('menu')
                                            <i class="fas fa-utensils"></i>
                                            @break
                                        @case('team')
                                            <i class="fas fa-users"></i>
                                            @break
                                        @case('testimonial')
                                            <i class="fas fa-quote-right"></i>
                                            @break
                                        @case('blog')
                                            <i class="fas fa-blog"></i>
                                            @break
                                        @case('brand')
                                            <i class="fas fa-handshake"></i>
                                            @break
                                        @default
                                            <i class="fas fa-cog"></i>
                                    @endswitch
                                </div>
                                <div class="admin-section-status">
                                    <div class="admin-toggle-switch">
                                        <input type="checkbox" id="status-{{ $section->id }}" 
                                               {{ $section->is_active ? 'checked' : '' }}
                                               onchange="toggleStatus('{{ $section->section_name }}', '{{ route('admin.section-settings.toggle-status', $section->section_name) }}')">
                                        <label for="status-{{ $section->id }}"></label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="admin-section-content">
                                <h4 class="admin-section-name">{{ ucwords(str_replace('_', ' ', $section->section_name)) }}</h4>
                                
                                @if($section->title)
                                    <div class="admin-section-detail">
                                        <strong>Title:</strong> {{ Str::limit($section->title, 30) }}
                                    </div>
                                @endif
                                
                                @if($section->subtitle)
                                    <div class="admin-section-detail">
                                        <strong>Subtitle:</strong> {{ Str::limit($section->subtitle, 40) }}
                                    </div>
                                @endif
                                
                                @if($section->description)
                                    <div class="admin-section-detail">
                                        <strong>Description:</strong> {{ Str::limit($section->description, 50) }}
                                    </div>
                                @endif
                                
                                <div class="admin-section-assets">
                                    @if($section->background_image)
                                        <span class="admin-badge admin-badge-primary">
                                            <i class="fas fa-image"></i> Background
                                        </span>
                                    @endif
                                    @if($section->left_shape_image)
                                        <span class="admin-badge admin-badge-info">
                                            <i class="fas fa-shapes"></i> Left Shape
                                        </span>
                                    @endif
                                    @if($section->right_shape_image)
                                        <span class="admin-badge admin-badge-success">
                                            <i class="fas fa-shapes"></i> Right Shape
                                        </span>
                                    @endif
                                </div>
                                
                                <div class="admin-section-actions">
                                    <a href="{{ route('admin.section-settings.edit', $section->section_name) }}" class="admin-btn admin-btn-primary">
                                        <i class="fas fa-edit"></i> Configure Section
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            @else
                <div class="admin-empty-state">
                    <div class="admin-empty-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <h3>No Section Settings Found</h3>
                    <p>Section settings will be automatically created when you run the seeder.</p>
                </div>
            @endif
        </div>
    </div>
</div>

<style>
.admin-section-card {
    background: var(--cardBg);
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    height: 100%;
}

.admin-section-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.admin-section-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
}

.admin-section-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: linear-gradient(135deg, var(--primaryColor), #ff6b6b);
    display: flex;
    align-items: center;
    justify-content: center;
}

.admin-section-icon i {
    font-size: 24px;
    color: white;
}

.admin-section-status {
    position: absolute;
    top: 15px;
    right: 15px;
}

.admin-section-name {
    font-size: 20px;
    font-weight: 600;
    color: var(--headingColor);
    margin-bottom: 15px;
    text-transform: capitalize;
}

.admin-section-detail {
    margin-bottom: 8px;
    font-size: 14px;
    color: var(--paraColor);
}

.admin-section-detail strong {
    color: var(--headingColor);
}

.admin-section-assets {
    margin: 15px 0 20px 0;
}

.admin-section-assets .admin-badge {
    margin-right: 5px;
    margin-bottom: 5px;
}

.admin-section-actions {
    margin-top: auto;
}

.admin-section-actions .admin-btn {
    width: 100%;
    text-align: center;
}
</style>

<script>
function toggleStatus(sectionName, url) {
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}
</script>
@endsection
