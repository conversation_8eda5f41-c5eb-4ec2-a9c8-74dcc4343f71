/* Reset and Base Styles */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f5f7fa;
}

/* Modern Floating Admin Sidebar */
.admin-sidebar {
    width: 280px;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    color: #2c3e50;
    position: fixed;
    top: 20px;
    left: 20px;
    height: calc(100vh - 40px);
    z-index: 1000;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.15),
        0 12px 30px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(102, 126, 234, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Floating animation */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-3px); }
}

.admin-sidebar {
    animation: float 6s ease-in-out infinite;
}

.admin-sidebar:hover {
    animation-play-state: paused;
    transform: translateY(-2px);
    box-shadow:
        0 30px 60px rgba(0, 0, 0, 0.2),
        0 15px 35px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(102, 126, 234, 0.2);
}

/* Mobile responsive adjustments */
@media (max-width: 991.98px) {
    .admin-sidebar {
        top: 0;
        left: 0;
        width: 280px;
        height: 100vh;
        border-radius: 0;
        transform: translateX(-100%);
        animation: none;
    }

    .admin-sidebar.active {
        transform: translateX(0);
    }
}

.admin-sidebar::-webkit-scrollbar {
    width: 6px;
}

.admin-sidebar::-webkit-scrollbar-track {
    background: #f8f9fa;
    border-radius: 4px;
}

.admin-sidebar::-webkit-scrollbar-thumb {
    background: #dee2e6;
    border-radius: 4px;
}

.admin-sidebar::-webkit-scrollbar-thumb:hover {
    background: #adb5bd;
}

/* Mobile Toggle Button */
.admin-sidebar-toggle {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1001;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 15px;
    font-size: 18px;
    cursor: pointer;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: none; /* Hidden by default, shown on mobile */
    align-items: center;
    justify-content: center;
}

.admin-sidebar-toggle:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

.admin-sidebar-toggle:active {
    transform: translateY(0);
}

/* Floating Animation for Sidebar */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-2px); }
}

.admin-sidebar {
    animation: float 6s ease-in-out infinite;
}

.admin-sidebar:hover {
    box-shadow:
        0 25px 70px rgba(0, 0, 0, 0.15),
        0 12px 35px rgba(0, 0, 0, 0.12),
        0 0 0 1px rgba(102, 126, 234, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transform: translateY(-1px);
}

/* Pulse animation for status indicator */
@keyframes pulse {
    0% { box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3); }
    50% { box-shadow: 0 2px 8px rgba(76, 175, 80, 0.6), 0 0 0 4px rgba(76, 175, 80, 0.1); }
    100% { box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3); }
}

.admin-status-indicator {
    animation: pulse 2s ease-in-out infinite;
}

/* Firefox scrollbar */
.admin-sidebar {
    scrollbar-width: thin;
    scrollbar-color: #dee2e6 #f8f9fa;
}

/* Modern Sidebar Header */
.admin-sidebar-header {
    padding: 30px 25px 25px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.admin-sidebar-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 25px;
    right: 20px;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.3), transparent);
}

.admin-sidebar-brand {
    display: flex;
    align-items: center;
    gap: 10px;
}

.admin-sidebar-logo {
    width: 45px;
    height: 45px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
    transition: transform 0.3s ease;
}

.admin-sidebar-title {
    font-size: 20px;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.5px;
}

.admin-sidebar-close {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: #6c757d;
    font-size: 16px;
    cursor: pointer;
    padding: 8px;
    border-radius: 10px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.admin-sidebar-close:hover {
    background: rgba(255, 255, 255, 0.2);
    color: #495057;
    transform: rotate(90deg);
}

/* Modern User Profile Section */
.admin-sidebar-user {
    padding: 25px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    display: flex;
    align-items: center;
    gap: 15px;
    flex-shrink: 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.admin-sidebar-user::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 25px;
    right: 20px;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.2), transparent);
}

.admin-user-avatar {
    position: relative;
}

.admin-avatar-wrapper {
    position: relative;
    display: inline-block;
}

.admin-user-avatar i {
    font-size: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 2px 4px rgba(102, 126, 234, 0.2));
}

.admin-status-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 14px;
    height: 14px;
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    border: 2px solid rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.admin-user-info {
    flex: 1;
}

.admin-user-name {
    margin: 0 0 4px 0;
    font-size: 17px;
    font-weight: 700;
    color: #2c3e50;
    letter-spacing: -0.3px;
}

.admin-user-role {
    font-size: 13px;
    color: #667eea;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.admin-user-dropdown-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: #667eea;
    cursor: pointer;
    padding: 8px;
    border-radius: 10px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.admin-user-dropdown-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: #764ba2;
    transform: rotate(180deg);
}

.admin-user-dropdown {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border-radius: 16px;
    padding: 12px 0;
    margin-top: 8px;
}

.admin-user-dropdown .dropdown-item {
    padding: 12px 20px;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.3s ease;
    font-weight: 500;
    border-radius: 0;
}

.admin-user-dropdown .dropdown-item:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    color: #667eea;
    transform: translateX(4px);
}

.admin-user-dropdown .dropdown-item i {
    width: 16px;
    text-align: center;
    opacity: 0.7;
}

/* Notifications */
.admin-sidebar-notifications {
    padding: 15px 20px;
    border-bottom: 1px solid #e5e5e5;
    flex-shrink: 0;
}

.admin-notification-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    background: #e3f2fd;
    border-radius: 6px;
    font-size: 14px;
    color: #1976d2;
}

.admin-notification-badge {
    background: #ff4757;
    color: white;
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: auto;
}

/* Modern Navigation - Scrollable Area */
.admin-sidebar-nav {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    min-height: 0;
    display: flex;
    flex-direction: column;
    padding: 20px 0;
}

/* Custom scrollbar for navigation area */
.admin-sidebar-nav::-webkit-scrollbar {
    width: 6px;
}

.admin-sidebar-nav::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    margin: 10px 0;
}

.admin-sidebar-nav::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.3) 0%, rgba(118, 75, 162, 0.3) 100%);
    border-radius: 3px;
    transition: background 0.3s ease;
}

.admin-sidebar-nav::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.5) 0%, rgba(118, 75, 162, 0.5) 100%);
}

.admin-nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
    width: 100%;
}

.admin-nav-item {
    margin-bottom: 4px;
    padding: 0 15px;
}

.admin-nav-link {
    display: flex;
    align-items: center;
    padding: 14px 20px;
    color: #2c3e50;
    text-decoration: none;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    border-radius: 16px;
    font-weight: 500;
    margin-bottom: 2px;
}

.admin-nav-link:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    color: #667eea;
    text-decoration: none;
    transform: translateX(4px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1);
}

.admin-nav-link.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
    transform: translateX(4px);
}

.admin-nav-link.active::before {
    content: '';
    position: absolute;
    left: -15px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

.admin-nav-icon {
    width: 22px;
    margin-right: 15px;
    text-align: center;
    font-size: 16px;
}

.admin-nav-text {
    flex: 1;
    font-size: 14px;
    font-weight: 500;
}

.admin-nav-arrow {
    font-size: 12px;
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0.7;
}

/* Modern Dropdown Navigation */
.admin-nav-dropdown.active .admin-nav-arrow {
    transform: rotate(90deg);
}

.admin-nav-submenu {
    list-style: none;
    padding: 8px 0;
    margin: 8px 15px 0 15px;
    display: none;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-nav-dropdown.active .admin-nav-submenu {
    display: block;
}

.admin-nav-sublink {
    display: flex;
    align-items: center;
    padding: 10px 20px;
    color: #6c757d;
    text-decoration: none;
    font-size: 13px;
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 2px 8px;
    font-weight: 500;
}

.admin-nav-sublink:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    transform: translateX(4px);
    text-decoration: none;
}

.admin-nav-sublink.active {
    background-color: #e3f2fd;
    color: #1976d2;
    font-weight: 600;
}

.admin-nav-sublink i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
}

/* Sidebar Footer */
.admin-sidebar-footer {
    padding: 20px;
    border-top: 1px solid #e5e5e5;
    flex-shrink: 0;
    background: #f8f9fa;
}

.admin-sidebar-footer-item {
    margin-bottom: 10px;
}

.admin-footer-link {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #6c757d;
    text-decoration: none;
    font-size: 14px;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.admin-footer-link:hover {
    background-color: #e9ecef;
    color: #007bff;
    text-decoration: none;
}

/* Main Layout Structure */
.admin-dashboard-wrapper {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background: #f5f7fa;
}

/* Topbar Positioning */
.admin-topbar {
    margin-left: 320px; /* 280px sidebar + 40px spacing */
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 0 0 15px 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 998;
    position: relative;
}

/* Main Content Area */
.admin-dashboard-content {
    margin-left: 320px !important; /* 280px sidebar + 40px spacing */
    padding: 30px !important;
    min-height: calc(100vh - 80px);
    transition: margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: transparent !important;
    flex: 1;
}

/* Enhanced Mobile Styles */
@media (max-width: 991.98px) {
    .admin-sidebar {
        top: 0;
        left: 0;
        width: 280px;
        height: 100vh;
        border-radius: 0;
        transform: translateX(-100%);
        animation: none; /* Disable floating animation on mobile */
        box-shadow: 0 0 50px rgba(0, 0, 0, 0.3);
    }

    .admin-sidebar.active {
        transform: translateX(0);
        box-shadow:
            0 0 50px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(102, 126, 234, 0.2);
    }

    .admin-sidebar::before {
        border-radius: 0;
    }

    .admin-topbar {
        margin-left: 0 !important;
        border-radius: 0;
    }

    .admin-dashboard-content {
        margin-left: 0 !important;
        padding: 20px !important;
    }

    .admin-sidebar-toggle {
        display: block !important;
        z-index: 1002;
    }

    /* Prevent body scroll when sidebar is open */
    body.sidebar-open {
        overflow: hidden;
    }
}

/* Tablet Styles */
@media (min-width: 768px) and (max-width: 991.98px) {
    .admin-sidebar {
        width: 280px;
        top: 0;
        left: 0;
        border-radius: 0;
        transform: translateX(-100%);
        animation: none;
    }

    .admin-sidebar.active {
        transform: translateX(0);
    }

    .admin-dashboard-wrapper .admin-dashboard-content {
        margin-left: 0 !important;
        padding: 15px !important;
        max-width: 100% !important;
    }

    .admin-sidebar-toggle {
        display: block !important;
    }
}

/* Desktop Styles */
@media (min-width: 992px) {
    .admin-sidebar-toggle {
        display: none;
    }

    .admin-sidebar:hover {
        animation-play-state: paused;
    }
}

/* Large Desktop Optimization */
@media (min-width: 1400px) {
    .admin-dashboard-wrapper .admin-dashboard-content {
        margin-left: 360px !important; /* Extra space for larger screens */
        padding: 30px !important;
    }
}

/* Enhanced Sidebar Overlay */
.admin-sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.7) 100%);
    backdrop-filter: blur(6px);
    -webkit-backdrop-filter: blur(6px);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.admin-sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Tablet specific styles */
@media (min-width: 768px) and (max-width: 991.98px) {
    .admin-topbar {
        margin-left: 0 !important;
        border-radius: 0;
    }

    .admin-dashboard-content {
        margin-left: 0 !important;
        padding: 25px !important;
    }

    .admin-sidebar-toggle {
        display: block !important;
    }
}

/* Body and layout adjustments */
body {
    margin: 0;
    padding: 0;
}

.admin-dashboard-wrapper {
    min-height: 100vh;
    display: block; /* Changed from flex to block for better flow */
    margin: 0;
    padding: 0;
}

/* Ensure proper spacing for topbar */
.admin-topbar {
    position: relative;
    z-index: 998;
    margin-left: 340px !important; /* Match content margin */
    transition: margin-left 0.3s ease;
}

@media (max-width: 991.98px) {
    .admin-topbar {
        margin-left: 0 !important;
    }
}

/* Creative Dashboard Header */
.admin-creative-header {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 35px;
    box-shadow:
        0 15px 50px rgba(0, 0, 0, 0.1),
        0 6px 20px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
    margin-bottom: 30px;
}

.admin-creative-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 50%;
    transform: translate(50%, -50%);
}

.admin-header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
}

.admin-header-main {
    display: flex;
    align-items: center;
    gap: 20px;
}

.admin-header-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.admin-header-title {
    font-size: 32px;
    font-weight: 800;
    background: linear-gradient(135deg, #2c3e50 0%, #667eea 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0 0 8px 0;
    letter-spacing: -1px;
}

.admin-header-subtitle {
    color: #6c757d;
    font-size: 16px;
    margin: 0;
    font-weight: 500;
}

.admin-header-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: flex-end;
}

.admin-header-time,
.admin-header-date {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #667eea;
    font-weight: 600;
    font-size: 14px;
}

.admin-header-time i,
.admin-header-date i {
    opacity: 0.7;
}

.admin-breadcrumb {
    position: relative;
    z-index: 2;
}

.admin-breadcrumb-item {
    color: #6c757d;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.admin-breadcrumb-item.active {
    color: #667eea;
}

/* Responsive header */
@media (max-width: 768px) {
    .admin-header-content {
        flex-direction: column;
        gap: 20px;
    }

    .admin-header-actions {
        flex-direction: row;
        align-items: center;
    }

    .admin-header-title {
        font-size: 24px;
    }
}

/* Modern Stat Cards */
.admin-stat-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 18px;
    padding: 30px;
    box-shadow:
        0 10px 40px rgba(0, 0, 0, 0.1),
        0 4px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    margin-bottom: 25px;
}

.admin-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.admin-stat-card:hover {
    transform: translateY(-8px);
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.15),
        0 8px 25px rgba(0, 0, 0, 0.1);
}

.admin-stat-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    margin-bottom: 15px;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.admin-stat-number {
    font-size: 28px;
    font-weight: 800;
    color: #2c3e50;
    margin-bottom: 8px;
    letter-spacing: -0.5px;
}

.admin-stat-label {
    color: #6c757d;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.admin-stat-change {
    font-size: 13px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 5px;
}

.admin-stat-change.positive {
    color: #28a745;
}

.admin-stat-change.negative {
    color: #dc3545;
}

.admin-stat-change i {
    font-size: 12px;
}

/* Modern Table Wrapper */
.admin-table-wrapper {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    margin-bottom: 20px;
}

.admin-table-header {
    padding: 25px 30px 20px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-table-title {
    font-size: 20px;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
    letter-spacing: -0.3px;
}

/* Modern Buttons */
.admin-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.admin-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
    text-decoration: none;
}

.admin-btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.admin-btn-secondary:hover {
    box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4);
}

.admin-btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.admin-btn-success:hover {
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
}

/* Footer Positioning */
.wsus__footer {
    margin-left: 320px; /* Match content margin */
    transition: margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@media (max-width: 991.98px) {
    .wsus__footer {
        margin-left: 0;
    }
}

/* Scroll Button Positioning */
.wsus__scroll_btn {
    right: 30px !important;
    bottom: 30px !important;
}

/* Override conflicting styles from main CSS */
html body .admin-dashboard-content,
html body main.admin-dashboard-content {
    margin-left: 320px !important;
    padding: 30px !important;
    background: transparent !important;
    min-height: calc(100vh - 80px) !important;
}

html body .admin-topbar,
html body section.admin-topbar {
    margin-left: 320px !important;
}

/* Mobile overrides */
@media (max-width: 991.98px) {
    html body .admin-dashboard-content,
    html body main.admin-dashboard-content {
        margin-left: 0 !important;
        padding: 20px !important;
    }

    html body .admin-topbar,
    html body section.admin-topbar {
        margin-left: 0 !important;
    }
}

/* Debug helper - temporary visual indicator */
.admin-dashboard-content::before {
    content: "Dashboard Content Area";
    position: fixed;
    top: 10px;
    right: 10px;
    background: rgba(102, 126, 234, 0.9);
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 12px;
    z-index: 9999;
    font-weight: bold;
}
