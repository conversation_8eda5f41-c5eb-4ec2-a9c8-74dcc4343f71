<?php

namespace Database\Seeders;

use App\Models\Advertisement;
use Illuminate\Database\Seeder;

class AdvertisementSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $ads = [
            [
                'title' => 'Fried Chicken',
                'description' => 'Lorem ipsum dolor sit amet consectetur.',
                'image' => 'uploads/custom-images/advertisement-2023-03-05-04-00-30-5264.png',
                'url' => 'product/onion-rings.html',
                'is_active' => true,
                'sort_order' => 1
            ],
            [
                'title' => 'Spicy Burger',
                'description' => 'Lorem ipsum dolor sit amet consectetur.',
                'image' => 'uploads/custom-images/advertisement-2023-03-05-04-01-56-2034.png',
                'url' => 'product/fried-chicken.html',
                'is_active' => true,
                'sort_order' => 2
            ],
            [
                'title' => 'New Year',
                'description' => 'Lorem ipsum dolor sit amet consectetur.',
                'image' => 'uploads/custom-images/advertisement-2023-03-05-04-03-43-9191.png',
                'url' => 'product/spicy-burger.html',
                'is_active' => true,
                'sort_order' => 3
            ],
            [
                'title' => 'Black Friday',
                'description' => 'Lorem ipsum dolor sit amet consectetur.',
                'image' => 'uploads/custom-images/advertisement-2023-03-05-04-06-17-9213.png',
                'url' => 'product/mozzarella-sticks.html',
                'is_active' => true,
                'sort_order' => 4
            ]
        ];

        foreach ($ads as $ad) {
            Advertisement::create($ad);
        }
    }
}
