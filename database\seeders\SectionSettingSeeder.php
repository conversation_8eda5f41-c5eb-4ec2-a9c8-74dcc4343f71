<?php

namespace Database\Seeders;

use App\Models\SectionSetting;
use Illuminate\Database\Seeder;

class SectionSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $sections = [
            [
                'section_name' => 'banner',
                'title' => null,
                'subtitle' => null,
                'description' => null,
                'background_image' => 'uploads/website-images/slider-bg-2022-12-12-11-51-08-2052.jpg',
                'left_shape_image' => 'uploads/website-images/slider-foreground1-2022-12-12-11-50-35-9330.png',
                'right_shape_image' => 'uploads/website-images/slider-foreground2-2022-12-12-11-48-49-1308.png',
                'is_active' => true
            ],
            [
                'section_name' => 'daily_offer',
                'title' => 'Daily Offer',
                'subtitle' => 'Up To 75% Off For This Day',
                'description' => 'Objectively pontificate quality models before intuitive information. Dramatically recaptiualize multifunctional.',
                'background_image' => null,
                'left_shape_image' => 'uploads/website-images/today_special_image-2022-12-15-02-32-21-7287.png',
                'right_shape_image' => null,
                'is_active' => true
            ],
            [
                'section_name' => 'menu',
                'title' => 'Food Menu',
                'subtitle' => 'Our Popular Delicious Foods',
                'description' => 'Objectively pontificate quality models before intuitive information. Dramatically recaptiualize multifunctional.',
                'background_image' => null,
                'left_shape_image' => 'uploads/website-images/menu_left_image-2022-12-15-02-46-22-9229.png',
                'right_shape_image' => 'uploads/website-images/menu_right_image-2022-12-15-02-46-42-1965.png',
                'is_active' => true
            ],
            [
                'section_name' => 'team',
                'title' => 'Our Team',
                'subtitle' => 'Meet Our Expert Chefs',
                'description' => 'Objectively pontificate quality models before intuitive information. Dramatically recaptiualize multifunctional.',
                'background_image' => 'user/images/chefs_bg.jpg',
                'left_shape_image' => 'uploads/website-images/chef_left_image-2022-12-15-02-59-31-5537.png',
                'right_shape_image' => 'uploads/website-images/chef_right_image-2022-12-15-03-00-31-1531.png',
                'is_active' => true
            ],
            [
                'section_name' => 'testimonial',
                'title' => 'Testimonial',
                'subtitle' => 'Our Customar Feedbacks',
                'description' => 'Objectively pontificate quality models before intuitive information. Dramatically recaptiualize multifunctional.',
                'background_image' => null,
                'left_shape_image' => null,
                'right_shape_image' => null,
                'is_active' => true
            ],
            [
                'section_name' => 'blog',
                'title' => 'Our Blogs',
                'subtitle' => 'Our Latest Foods Blog',
                'description' => 'Objectively pontificate quality models before intuitive information. Dramatically recaptiualize multifunctional.',
                'background_image' => 'uploads/website-images/blog_background-2022-12-15-03-17-45-5083.jpg',
                'left_shape_image' => null,
                'right_shape_image' => null,
                'is_active' => true
            ],
            [
                'section_name' => 'brand',
                'title' => null,
                'subtitle' => null,
                'description' => null,
                'background_image' => 'uploads/website-images/counter-bg-2023-03-06-09-34-36-3312.jpg',
                'left_shape_image' => null,
                'right_shape_image' => null,
                'is_active' => true
            ]
        ];

        foreach ($sections as $section) {
            SectionSetting::create($section);
        }
    }
}
