<!DOCTYPE html>
<html lang="en">

@include('visitors.includes.header')
<body>

    <div class="" id="preloader">
        <div class="img d-none">
            <img src="{{ asset('uploads/website-images/Spinner.gif') }}" alt="UniFood" class="img-fluid">
        </div>
    </div>

    <!--=============================
        TOPBAR START
    ==============================-->
    @include('visitors.includes.topbar')
    <!--=============================
        TOPBAR END
    ==============================-->

    <!--=============================
        MENU START
    ==============================-->
    @include('visitors.includes.nav')
    <!--=============================
        MENU END
    ==============================-->

    <!--=============================
        BREADCRUMB START
    ==============================-->
    <section class="wsus__breadcrumb" style="background: url({{ asset('uploads/website-images/breadcrumb-2022-12-13-12-06-33-5633.jpg') }});">
        <div class="wsus__breadcrumb_overlay">
            <div class="container">
                <div class="row">
                    <div class="col-12 wow fadeInUp" data-wow-duration="1s">
                        <nav aria-label="breadcrumb">
                            <h1>Shopping Cart</h1>
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                                <li class="breadcrumb-item active" aria-current="page">Cart</li>
                            </ol>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!--=============================
        BREADCRUMB END
    ==============================-->

    <!--=============================
        CART PAGE START
    ==============================-->
    <section class="wsus__cart_view mt_100 xs_mt_70">
        <div class="container">
            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <div class="row">
                <div class="col-xl-9">
                    <div class="wsus__cart_list">
                        <div class="table-responsive">
                            <table class="table">
                                <tbody>
                                    <tr class="d-flex">
                                        <th class="wsus__pro_img col-xl-2 col-6">
                                            Product
                                        </th>

                                        <th class="wsus__pro_name col-xl-3 col-6">
                                            Details
                                        </th>

                                        <th class="wsus__pro_tk col-xl-2 d-none d-md-block">
                                            Price
                                        </th>

                                        <th class="wsus__pro_tk col-xl-3 col-12">
                                            Quantity
                                        </th>

                                        <th class="wsus__pro_tk col-xl-2 d-none d-md-block">
                                            Total
                                        </th>
                                    </tr>

                                    @if(isset($cart->items) && $cart->items->count() > 0)
                                        @foreach($cart->items as $item)
                                            <tr class="d-flex cart-item" data-product-id="{{ $item->product_id }}">
                                                <td class="wsus__pro_img col-xl-2 col-6">
                                                    <img src="{{ asset($item->product->image) }}" alt="{{ $item->product->name }}" class="img-fluid w-100">
                                                    <a href="#" class="del_icon remove-item"><i class="fal fa-times"></i></a>
                                                </td>

                                                <td class="wsus__pro_name col-xl-3 col-6">
                                                    <a href="{{ route('products.show', $item->product->slug) }}">{{ $item->product->name }}</a>
                                                    <span>{{ $item->product->category }}</span>
                                                    <p class="d-block d-md-none">Price: ${{ number_format($item->price, 2) }}</p>
                                                </td>

                                                <td class="wsus__pro_tk col-xl-2 d-none d-md-block">
                                                    <h6>${{ number_format($item->price, 2) }}</h6>
                                                </td>

                                                <td class="wsus__pro_tk col-xl-3 col-12">
                                                    <div class="wsus__quentity">
                                                        <button class="btn btn-danger btn-sm quantity-btn" data-action="decrease">
                                                            <i class="fal fa-minus"></i>
                                                        </button>
                                                        <input type="text" class="quantity-input" value="{{ $item->quantity }}" readonly>
                                                        <button class="btn btn-success btn-sm quantity-btn" data-action="increase">
                                                            <i class="fal fa-plus"></i>
                                                        </button>
                                                    </div>
                                                    <h5 class="d-block d-md-none mt-2">Total: $<span class="item-total">{{ number_format($item->quantity * $item->price, 2) }}</span></h5>
                                                </td>

                                                <td class="wsus__pro_tk col-xl-2 d-none d-md-block">
                                                    <h6>$<span class="item-total">{{ number_format($item->quantity * $item->price, 2) }}</span></h6>
                                                </td>
                                            </tr>
                                        @endforeach
                                    @else
                                        <tr>
                                            <td colspan="5" class="text-center py-5">
                                                <div class="wsus__empty_cart">
                                                    <i class="fal fa-shopping-cart" style="font-size: 60px; color: #ccc;"></i>
                                                    <h4 class="mt-3">Your cart is empty</h4>
                                                    <p>Add some delicious items to your cart</p>
                                                    <a href="{{ route('products') }}" class="common_btn">Continue Shopping</a>
                                                </div>
                                            </td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                @if(isset($cart->items) && $cart->items->count() > 0)
                    <div class="col-xl-3">
                        <div class="wsus__cart_list_footer_button">
                            <h6>Total Cart</h6>
                            <p>Subtotal: <span id="cart-subtotal">${{ number_format($cart->subtotal, 2) }}</span></p>
                            <p>Delivery: <span>Free</span></p>
                            <p class="total"><span>Total: <span id="cart-total">${{ number_format($cart->subtotal, 2) }}</span></span></p>
                            
                            <form>
                                <input type="text" placeholder="Coupon Code" class="mb-3">
                                <button type="button" class="common_btn">Apply Coupon</button>
                            </form>
                            
                            <a class="common_btn mt-4 w-100 text-center" href="{{ route('checkout.index') }}">Checkout</a>
                            <a class="common_btn mt-2 w-100 text-center" href="{{ route('products') }}">Continue Shopping</a>
                            
                            <button type="button" class="btn btn-outline-danger mt-2 w-100" id="clear-cart">
                                <i class="fal fa-trash"></i> Clear Cart
                            </button>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </section>
    <!--=============================
        CART PAGE END
    ==============================-->

    <!--=============================
        FOOTER START
    ==============================-->
    @include('visitors.includes.footer')
    <!--=============================
        FOOTER END
    ==============================-->

    <!--=============================
        SCROLL BUTTON START
    ==============================-->
    <div class="wsus__scroll_btn">
        Go to top
    </div>
    <!--=============================
        SCROLL BUTTON END
    ==============================-->

    <!--bootstrap js-->
    <script src="{{ asset('user/js/bootstrap.bundle.min.js') }}"></script>
    <!--font-awesome js-->
    <script src="{{ asset('user/js/Font-Awesome.js') }}"></script>
    <!-- slick slider -->
    <script src="{{ asset('user/js/slick.min.js') }}"></script>
    <!-- isotop js -->
    <script src="{{ asset('user/js/isotope.pkgd.min.js') }}"></script>
    <!-- simplyCountdownjs -->
    <script src="{{ asset('user/js/simplyCountdown.js') }}"></script>
    <!-- counter up js -->
    <script src="{{ asset('user/js/jquery.waypoints.min.js') }}"></script>
    <script src="{{ asset('user/js/jquery.countup.min.js') }}"></script>
    <!-- nice select js -->
    <script src="{{ asset('user/js/jquery.nice-select.min.js') }}"></script>
    <!-- venobox js -->
    <script src="{{ asset('user/js/venobox.min.js') }}"></script>
    <!-- sticky sidebar js -->
    <script src="{{ asset('user/js/sticky_sidebar.js') }}"></script>
    <!-- wow js -->
    <script src="{{ asset('user/js/wow.min.js') }}"></script>
    <!-- ex zoom js -->
    <script src="{{ asset('user/js/jquery.exzoom.js') }}"></script>

    <!--main/custom js-->
    <script src="{{ asset('user/js/main.js') }}"></script>
    <script src="{{ asset('toastr/toastr.min.js') }}"></script>

    <script>
        $(document).ready(function() {
            // Update quantity
            $('.quantity-btn').on('click', function() {
                const action = $(this).data('action');
                const cartItem = $(this).closest('.cart-item');
                const productId = cartItem.data('product-id');
                const quantityInput = cartItem.find('.quantity-input');
                let quantity = parseInt(quantityInput.val());

                if (action === 'increase') {
                    quantity++;
                } else if (action === 'decrease' && quantity > 1) {
                    quantity--;
                }

                updateCartQuantity(productId, quantity, cartItem);
            });

            // Remove item
            $('.remove-item').on('click', function(e) {
                e.preventDefault();
                const cartItem = $(this).closest('.cart-item');
                const productId = cartItem.data('product-id');
                
                if (confirm('Are you sure you want to remove this item?')) {
                    removeCartItem(productId, cartItem);
                }
            });

            // Clear cart
            $('#clear-cart').on('click', function() {
                if (confirm('Are you sure you want to clear your cart?')) {
                    clearCart();
                }
            });

            function updateCartQuantity(productId, quantity, cartItem) {
                $.ajax({
                    url: `/cart/update/${productId}`,
                    method: 'PUT',
                    data: {
                        quantity: quantity,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            cartItem.find('.quantity-input').val(quantity);
                            const itemTotal = (quantity * parseFloat(cartItem.find('td:nth-child(3) h6').text().replace('$', ''))).toFixed(2);
                            cartItem.find('.item-total').text(itemTotal);
                            
                            $('#cart-subtotal').text('$' + response.cart_subtotal);
                            $('#cart-total').text('$' + response.cart_subtotal);
                            updateCartCounter(response.cart_count);
                            
                            toastr.success('Cart updated successfully');
                        }
                    },
                    error: function() {
                        toastr.error('Failed to update cart');
                    }
                });
            }

            function removeCartItem(productId, cartItem) {
                $.ajax({
                    url: `/cart/remove/${productId}`,
                    method: 'DELETE',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            cartItem.fadeOut(300, function() {
                                $(this).remove();
                                
                                if ($('.cart-item').length === 0) {
                                    location.reload();
                                }
                            });
                            
                            $('#cart-subtotal').text('$' + response.cart_subtotal);
                            $('#cart-total').text('$' + response.cart_subtotal);
                            updateCartCounter(response.cart_count);
                            
                            toastr.success('Item removed from cart');
                        }
                    },
                    error: function() {
                        toastr.error('Failed to remove item');
                    }
                });
            }

            function clearCart() {
                $.ajax({
                    url: '/cart/clear',
                    method: 'DELETE',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            location.reload();
                        }
                    },
                    error: function() {
                        toastr.error('Failed to clear cart');
                    }
                });
            }

            function updateCartCounter(count) {
                $('.cart-count').text(count);
            }
        });
    </script>

</body>
</html>
