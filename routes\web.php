<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

// Visitor routes
Route::get('/', [App\Http\Controllers\HomeController::class, 'index'])->name('home');

Route::get('/about', function () {
    return view('visitors.about');
})->name('about');

Route::get('/products', function () {
    return view('visitors.products');
})->name('products');

Route::get('/contact', function () {
    return view('visitors.contact');
})->name('contact');

// Pages dropdown routes
Route::get('/our-chef', function () {
    return view('visitors.our-chef');
})->name('our-chef');

Route::get('/testimonial', function () {
    return view('visitors.testimonial');
})->name('testimonial');

Route::get('/faq', function () {
    return view('visitors.faq');
})->name('faq');

Route::get('/privacy-policy', function () {
    return view('visitors.privacy-policy');
})->name('privacy-policy');

Route::get('/terms-and-condition', function () {
    return view('visitors.terms-and-condition');
})->name('terms-and-condition');

Route::get('/page/more-page-one', function () {
    return view('visitors.more-page-one');
})->name('more-page-one');

Route::get('/page/more-page-two', function () {
    return view('visitors.more-page-two');
})->name('more-page-two');

// Blog routes
Route::get('/blogs', function () {
    return view('visitors.blogs');
})->name('blogs');

// Admin routes
Route::get('/dashboard', function () {
    return view('admin.dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// Admin Content Management Routes
Route::middleware(['auth', 'verified'])->prefix('admin')->name('admin.')->group(function () {
    // Hero Sliders
    Route::resource('hero-sliders', App\Http\Controllers\Admin\HeroSliderController::class);
    Route::post('hero-sliders/{heroSlider}/toggle-status', [App\Http\Controllers\Admin\HeroSliderController::class, 'toggleStatus'])->name('hero-sliders.toggle-status');

    // Why Choose Us
    Route::resource('why-choose-us', App\Http\Controllers\Admin\WhyChooseUsController::class);
    Route::post('why-choose-us/{whyChooseUs}/toggle-status', [App\Http\Controllers\Admin\WhyChooseUsController::class, 'toggleStatus'])->name('why-choose-us.toggle-status');

    // Daily Offers
    Route::resource('daily-offers', App\Http\Controllers\Admin\DailyOfferController::class);
    Route::post('daily-offers/{dailyOffer}/toggle-status', [App\Http\Controllers\Admin\DailyOfferController::class, 'toggleStatus'])->name('daily-offers.toggle-status');

    // Team Members
    Route::resource('team-members', App\Http\Controllers\Admin\TeamMemberController::class);
    Route::post('team-members/{teamMember}/toggle-status', [App\Http\Controllers\Admin\TeamMemberController::class, 'toggleStatus'])->name('team-members.toggle-status');

    // Testimonials
    Route::resource('testimonials', App\Http\Controllers\Admin\TestimonialController::class);
    Route::post('testimonials/{testimonial}/toggle-status', [App\Http\Controllers\Admin\TestimonialController::class, 'toggleStatus'])->name('testimonials.toggle-status');

    // Counters
    Route::resource('counters', App\Http\Controllers\Admin\CounterController::class);
    Route::post('counters/{counter}/toggle-status', [App\Http\Controllers\Admin\CounterController::class, 'toggleStatus'])->name('counters.toggle-status');

    // Blog Posts
    Route::resource('blog-posts', App\Http\Controllers\Admin\BlogPostController::class);
    Route::post('blog-posts/{blogPost}/toggle-status', [App\Http\Controllers\Admin\BlogPostController::class, 'toggleStatus'])->name('blog-posts.toggle-status');

    // Brand Partners
    Route::resource('brand-partners', App\Http\Controllers\Admin\BrandPartnerController::class);
    Route::post('brand-partners/{brandPartner}/toggle-status', [App\Http\Controllers\Admin\BrandPartnerController::class, 'toggleStatus'])->name('brand-partners.toggle-status');

    // Advertisements
    Route::resource('advertisements', App\Http\Controllers\Admin\AdvertisementController::class);
    Route::post('advertisements/{advertisement}/toggle-status', [App\Http\Controllers\Admin\AdvertisementController::class, 'toggleStatus'])->name('advertisements.toggle-status');

    // App Download
    Route::get('app-download', [App\Http\Controllers\Admin\AppDownloadController::class, 'index'])->name('app-download.index');
    Route::get('app-download/edit', [App\Http\Controllers\Admin\AppDownloadController::class, 'edit'])->name('app-download.edit');
    Route::put('app-download', [App\Http\Controllers\Admin\AppDownloadController::class, 'update'])->name('app-download.update');
    Route::post('app-download/toggle-status', [App\Http\Controllers\Admin\AppDownloadController::class, 'toggleStatus'])->name('app-download.toggle-status');

    // Section Settings
    Route::get('section-settings', [App\Http\Controllers\Admin\SectionSettingController::class, 'index'])->name('section-settings.index');
    Route::get('section-settings/{sectionName}/edit', [App\Http\Controllers\Admin\SectionSettingController::class, 'edit'])->name('section-settings.edit');
    Route::put('section-settings/{sectionName}', [App\Http\Controllers\Admin\SectionSettingController::class, 'update'])->name('section-settings.update');
    Route::post('section-settings/{sectionName}/toggle-status', [App\Http\Controllers\Admin\SectionSettingController::class, 'toggleStatus'])->name('section-settings.toggle-status');
});

Route::get('/admin/products', function () {
    return view('admin.products');
})->middleware(['auth', 'verified'])->name('admin.products');

// Profile routes
Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';
