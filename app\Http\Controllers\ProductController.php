<?php

namespace App\Http\Controllers;

use App\Models\Product;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    /**
     * Display a listing of products
     */
    public function index(Request $request)
    {
        $query = Product::active()->ordered();

        // Filter by category if provided
        if ($request->has('category') && $request->category) {
            $query->byCategory($request->category);
        }

        // Search functionality
        if ($request->has('search') && $request->search) {
            $query->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
        }

        $products = $query->get();
        $categories = Product::active()->distinct()->pluck('category');

        return view('visitors.products', compact('products', 'categories'));
    }

    /**
     * Display the specified product
     */
    public function show(Product $product)
    {
        if (!$product->is_active) {
            abort(404);
        }

        // Get related products from same category
        $relatedProducts = Product::active()
            ->byCategory($product->category)
            ->where('id', '!=', $product->id)
            ->ordered()
            ->limit(4)
            ->get();

        return view('visitors.product-detail', compact('product', 'relatedProducts'));
    }
}
