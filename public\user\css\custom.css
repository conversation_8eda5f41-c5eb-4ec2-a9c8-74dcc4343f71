/* Custom CSS Variables and Styles for UniFood */
:root {
    --colorPrimary: #eb0029;
    --paraColor: #494949;
    --colorBlack: #010f1c;
    --colorWhite: #ffffff;
    --paraFont: 'Barlow', sans-serif;
    --headingFont: 'Jost', sans-serif;
    --cursiveFont: 'Lobster', cursive;
    --boxShadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
    --gradiantBg: linear-gradient(0deg, rgba(156, 3, 30, 1) 0%, rgba(235, 0, 41, 1) 100%);
    --gradiantHoverBg: linear-gradient(0deg, rgba(235, 0, 41, 1) 0%, rgba(156, 3, 30, 1) 100%);
}

/* Footer overlay styling */
.footer_overlay {
    background: #b90424fa !important;
}

/* Topbar icon styling */
.topbar_icon li a {
    background: #ca0628 !important;
}

/* Loading spinner styles */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Placeholder image styles for missing assets */
.image-placeholder {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    font-size: 14px;
    text-align: center;
    min-height: 200px;
}

.image-placeholder::before {
    content: "Image Loading...";
}

/* Fallback styles for missing background images */
.slider-bg-fallback {
    background: linear-gradient(135deg, var(--colorPrimary) 0%, #c41e3a 100%);
}

.chef-bg-fallback {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

.counter-bg-fallback {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.blog-bg-fallback {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}
