<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\DailyOffer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class DailyOfferController extends Controller
{
    public function index()
    {
        $offers = DailyOffer::ordered()->get();
        return view('admin.content.daily-offers.index', compact('offers'));
    }

    public function create()
    {
        return view('admin.content.daily-offers.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'discount_percentage' => 'required|integer|min:1|max:100',
            'product_url' => 'nullable|string|max:255',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0'
        ]);

        $data = $request->all();
        
        if ($request->hasFile('image')) {
            $data['image'] = $request->file('image')->store('daily-offers', 'public');
        }

        DailyOffer::create($data);

        return redirect()->route('admin.daily-offers.index')
            ->with('success', 'Daily offer created successfully.');
    }

    public function show(DailyOffer $dailyOffer)
    {
        return view('admin.content.daily-offers.show', compact('dailyOffer'));
    }

    public function edit(DailyOffer $dailyOffer)
    {
        return view('admin.content.daily-offers.edit', compact('dailyOffer'));
    }

    public function update(Request $request, DailyOffer $dailyOffer)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'discount_percentage' => 'required|integer|min:1|max:100',
            'product_url' => 'nullable|string|max:255',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0'
        ]);

        $data = $request->all();
        
        if ($request->hasFile('image')) {
            // Delete old image
            if ($dailyOffer->image && Storage::disk('public')->exists($dailyOffer->image)) {
                Storage::disk('public')->delete($dailyOffer->image);
            }
            $data['image'] = $request->file('image')->store('daily-offers', 'public');
        }

        $dailyOffer->update($data);

        return redirect()->route('admin.daily-offers.index')
            ->with('success', 'Daily offer updated successfully.');
    }

    public function destroy(DailyOffer $dailyOffer)
    {
        // Delete image
        if ($dailyOffer->image && Storage::disk('public')->exists($dailyOffer->image)) {
            Storage::disk('public')->delete($dailyOffer->image);
        }

        $dailyOffer->delete();

        return redirect()->route('admin.daily-offers.index')
            ->with('success', 'Daily offer deleted successfully.');
    }

    public function toggleStatus(DailyOffer $dailyOffer)
    {
        $dailyOffer->update(['is_active' => !$dailyOffer->is_active]);
        
        return response()->json([
            'success' => true,
            'message' => 'Status updated successfully.',
            'is_active' => $dailyOffer->is_active
        ]);
    }
}
