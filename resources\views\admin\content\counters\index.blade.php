@extends('admin.admin-layout')

@section('content')
<div class="row">
    <!-- Creative Header -->
    <div class="col-12">
        <div class="admin-creative-header mb-4">
            <div class="admin-header-content">
                <div class="admin-header-main">
                    <div class="admin-header-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="admin-header-text">
                        <h1 class="admin-header-title">Statistics Counters</h1>
                        <p class="admin-header-subtitle">Manage your achievement counters and statistics</p>
                    </div>
                </div>
                <div class="admin-header-actions">
                    <a href="{{ route('admin.counters.create') }}" class="admin-btn">
                        <i class="fas fa-plus"></i> Add New Counter
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="admin-table-wrapper">
            <div class="admin-table-header">
                <h3 class="admin-table-title">All Counters</h3>
                <div class="admin-table-actions">
                    <span class="admin-badge admin-badge-info">{{ $counters->count() }} Total</span>
                </div>
            </div>
            
            @if($counters->count() > 0)
                <div class="row">
                    @foreach($counters as $counter)
                    <div class="col-lg-6 col-xl-3 mb-4">
                        <div class="admin-counter-card">
                            <div class="admin-counter-header">
                                <div class="admin-counter-icon">
                                    <i class="{{ $counter->icon }}"></i>
                                </div>
                                <div class="admin-counter-status">
                                    <div class="admin-toggle-switch">
                                        <input type="checkbox" id="status-{{ $counter->id }}" 
                                               {{ $counter->is_active ? 'checked' : '' }}
                                               onchange="toggleStatus({{ $counter->id }}, '{{ route('admin.counters.toggle-status', $counter) }}')">
                                        <label for="status-{{ $counter->id }}"></label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="admin-counter-content">
                                <div class="admin-counter-number">{{ number_format($counter->count) }}</div>
                                <h4 class="admin-counter-title">{{ $counter->title }}</h4>
                                
                                <div class="admin-counter-meta">
                                    <span class="admin-badge admin-badge-secondary">Order: {{ $counter->sort_order }}</span>
                                    @if($counter->background_image)
                                        <span class="admin-badge admin-badge-info">Has Background</span>
                                    @endif
                                </div>
                                
                                <div class="admin-counter-actions">
                                    <a href="{{ route('admin.counters.show', $counter) }}" class="admin-btn admin-btn-sm admin-btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.counters.edit', $counter) }}" class="admin-btn admin-btn-sm admin-btn-warning">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('admin.counters.destroy', $counter) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="admin-btn admin-btn-sm admin-btn-danger" 
                                                onclick="return confirm('Are you sure you want to delete this counter?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            @else
                <div class="admin-empty-state">
                    <div class="admin-empty-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <h3>No Counters Found</h3>
                    <p>Add your first counter to showcase your restaurant's achievements and statistics.</p>
                    <a href="{{ route('admin.counters.create') }}" class="admin-btn">
                        <i class="fas fa-plus"></i> Add First Counter
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<style>
.admin-counter-card {
    background: var(--cardBg);
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    text-align: center;
    overflow: hidden;
}

.admin-counter-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.admin-counter-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primaryColor), #ff6b6b);
}

.admin-counter-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
}

.admin-counter-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primaryColor), #ff6b6b);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.admin-counter-icon i {
    font-size: 24px;
    color: white;
}

.admin-counter-status {
    position: absolute;
    top: 15px;
    right: 15px;
}

.admin-counter-number {
    font-size: 36px;
    font-weight: 700;
    color: var(--primaryColor);
    margin-bottom: 10px;
    background: linear-gradient(135deg, var(--primaryColor), #ff6b6b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.admin-counter-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--headingColor);
    margin-bottom: 15px;
}

.admin-counter-meta {
    margin-bottom: 20px;
}

.admin-counter-meta .admin-badge {
    margin-right: 5px;
    margin-bottom: 5px;
}

.admin-counter-actions {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.admin-counter-actions .admin-btn {
    flex: 1;
    max-width: 40px;
}
</style>

<script>
function toggleStatus(id, url) {
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}
</script>
@endsection
