<?php

namespace Database\Seeders;

use App\Models\HeroSlider;
use Illuminate\Database\Seeder;

class HeroSliderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $sliders = [
            [
                'title' => 'Special Deals Today',
                'subtitle' => 'Fast Food & Restaurants',
                'description' => 'Lorem ipsum, dolor sit amet consectetur adipisicing elit. Ipsum fugit minima et debitis ut distinctio optio qui voluptate natus.',
                'button_text' => 'Shop now',
                'button_url' => 'product/fried-chicken.html',
                'image' => 'uploads/custom-images/slider-2023-03-05-04-12-06-5809.png',
                'offer_text' => '35% off',
                'is_active' => true,
                'sort_order' => 1
            ],
            [
                'title' => 'Delicious Food Options',
                'subtitle' => 'Satisfy Your Cravings',
                'description' => 'Lorem ipsum, dolor sit amet consectetur adipisicing elit. Ipsum fugit minima et debitis ut distinctio optio qui voluptate natus.',
                'button_text' => 'Shop now',
                'button_url' => 'product/daria-shevtsova.html',
                'image' => 'uploads/custom-images/slider-2023-03-05-04-14-24-6461.png',
                'offer_text' => '25% Off',
                'is_active' => true,
                'sort_order' => 2
            ],
            [
                'title' => 'Mouth-Watering Dishes',
                'subtitle' => 'Try Something New',
                'description' => 'Lorem ipsum, dolor sit amet consectetur adipisicing elit. Ipsum fugit minima et debitis ut distinctio optio qui voluptate natus.',
                'button_text' => 'Shop now',
                'button_url' => 'product/onion-rings.html',
                'image' => 'uploads/custom-images/slider-2023-03-05-04-15-55-8570.png',
                'offer_text' => '20% off',
                'is_active' => true,
                'sort_order' => 3
            ]
        ];

        foreach ($sliders as $slider) {
            HeroSlider::create($slider);
        }
    }
}
