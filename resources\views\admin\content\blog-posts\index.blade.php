@extends('admin.admin-layout')

@section('content')
<div class="row">
    <!-- Creative Header -->
    <div class="col-12">
        <div class="admin-creative-header mb-4">
            <div class="admin-header-content">
                <div class="admin-header-main">
                    <div class="admin-header-icon">
                        <i class="fas fa-blog"></i>
                    </div>
                    <div class="admin-header-text">
                        <h1 class="admin-header-title">Blog Posts</h1>
                        <p class="admin-header-subtitle">Manage your restaurant's blog articles and news</p>
                    </div>
                </div>
                <div class="admin-header-actions">
                    <a href="{{ route('admin.blog-posts.create') }}" class="admin-btn">
                        <i class="fas fa-plus"></i> Add New Post
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="admin-table-wrapper">
            <div class="admin-table-header">
                <h3 class="admin-table-title">All Blog Posts</h3>
                <div class="admin-table-actions">
                    <span class="admin-badge admin-badge-info">{{ $posts->count() }} Total</span>
                </div>
            </div>
            
            @if($posts->count() > 0)
                <div class="row">
                    @foreach($posts as $post)
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="admin-blog-card">
                            <div class="admin-blog-image">
                                <img src="{{ asset('storage/' . $post->image) }}" alt="{{ $post->title }}">
                                <div class="admin-blog-status">
                                    <div class="admin-toggle-switch">
                                        <input type="checkbox" id="status-{{ $post->id }}" 
                                               {{ $post->is_active ? 'checked' : '' }}
                                               onchange="toggleStatus({{ $post->id }}, '{{ route('admin.blog-posts.toggle-status', $post) }}')">
                                        <label for="status-{{ $post->id }}"></label>
                                    </div>
                                </div>
                                <div class="admin-blog-category">
                                    <span>{{ $post->category }}</span>
                                </div>
                            </div>
                            
                            <div class="admin-blog-content">
                                <div class="admin-blog-meta">
                                    <span class="admin-blog-date">
                                        <i class="fas fa-calendar"></i>
                                        {{ $post->published_at ? $post->published_at->format('M d, Y') : 'Draft' }}
                                    </span>
                                    <span class="admin-blog-author">
                                        <i class="fas fa-user"></i>
                                        {{ $post->author }}
                                    </span>
                                </div>
                                
                                <h4 class="admin-blog-title">{{ Str::limit($post->title, 50) }}</h4>
                                
                                @if($post->excerpt)
                                    <p class="admin-blog-excerpt">{{ Str::limit($post->excerpt, 80) }}</p>
                                @else
                                    <p class="admin-blog-excerpt">{{ Str::limit(strip_tags($post->content), 80) }}</p>
                                @endif
                                
                                <div class="admin-blog-footer">
                                    <div class="admin-blog-badges">
                                        <span class="admin-badge admin-badge-secondary">Order: {{ $post->sort_order }}</span>
                                        @if($post->published_at && $post->published_at <= now())
                                            <span class="admin-badge admin-badge-success">Published</span>
                                        @else
                                            <span class="admin-badge admin-badge-warning">Draft</span>
                                        @endif
                                    </div>
                                    
                                    <div class="admin-blog-actions">
                                        <a href="{{ route('admin.blog-posts.show', $post) }}" class="admin-btn admin-btn-sm admin-btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.blog-posts.edit', $post) }}" class="admin-btn admin-btn-sm admin-btn-warning">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ route('admin.blog-posts.destroy', $post) }}" method="POST" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="admin-btn admin-btn-sm admin-btn-danger" 
                                                    onclick="return confirm('Are you sure you want to delete this blog post?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            @else
                <div class="admin-empty-state">
                    <div class="admin-empty-icon">
                        <i class="fas fa-blog"></i>
                    </div>
                    <h3>No Blog Posts Found</h3>
                    <p>Create your first blog post to share news and stories with your customers.</p>
                    <a href="{{ route('admin.blog-posts.create') }}" class="admin-btn">
                        <i class="fas fa-plus"></i> Create First Post
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<style>
.admin-blog-card {
    background: var(--cardBg);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}

.admin-blog-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.admin-blog-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.admin-blog-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.admin-blog-card:hover .admin-blog-image img {
    transform: scale(1.05);
}

.admin-blog-status {
    position: absolute;
    top: 15px;
    right: 15px;
}

.admin-blog-category {
    position: absolute;
    bottom: 15px;
    left: 15px;
}

.admin-blog-category span {
    background: var(--primaryColor);
    color: white;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
}

.admin-blog-content {
    padding: 20px;
}

.admin-blog-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    font-size: 12px;
    color: var(--paraColor);
}

.admin-blog-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.admin-blog-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--headingColor);
    margin-bottom: 10px;
    line-height: 1.3;
}

.admin-blog-excerpt {
    color: var(--paraColor);
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 20px;
}

.admin-blog-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.admin-blog-badges .admin-badge {
    margin-right: 5px;
}

.admin-blog-actions {
    display: flex;
    gap: 5px;
}
</style>

<script>
function toggleStatus(id, url) {
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}
</script>
@endsection
