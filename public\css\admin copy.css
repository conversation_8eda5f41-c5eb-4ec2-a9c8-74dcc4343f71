/* Admin Sidebar Scrollable Fix */
.admin-sidebar {
    width: 280px;
    background: #ffffff;
    color: #333333;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    transition: transform 0.3s ease;
    box-shadow: 2px 0 15px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border-right: 1px solid #e5e5e5;
}

/* Ensure sidebar is visible on desktop */
@media (min-width: 992px) {
    .admin-sidebar {
        transform: translateX(0) !important;
    }
}

.admin-sidebar::-webkit-scrollbar {
    width: 6px;
}

.admin-sidebar::-webkit-scrollbar-track {
    background: #f8f9fa;
    border-radius: 4px;
}

.admin-sidebar::-webkit-scrollbar-thumb {
    background: #dee2e6;
    border-radius: 4px;
}

.admin-sidebar::-webkit-scrollbar-thumb:hover {
    background: #adb5bd;
}

/* Firefox scrollbar */
.admin-sidebar {
    scrollbar-width: thin;
    scrollbar-color: #dee2e6 #f8f9fa;
}

/* Sidebar Header */
.admin-sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #e5e5e5;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
    background: #f8f9fa;
}

.admin-sidebar-brand {
    display: flex;
    align-items: center;
    gap: 12px;
}

.admin-sidebar-logo {
    width: 40px;
    height: 40px;
    border-radius: 8px;
}

.admin-sidebar-title {
    font-size: 18px;
    font-weight: 600;
    color: #495057;
}

.admin-sidebar-close {
    background: none;
    border: none;
    color: #6c757d;
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.admin-sidebar-close:hover {
    background-color: #e9ecef;
    color: #495057;
}

/* User Profile Section */
.admin-sidebar-user {
    padding: 20px;
    border-bottom: 1px solid #e5e5e5;
    display: flex;
    align-items: center;
    gap: 12px;
    flex-shrink: 0;
    background: #f8f9fa;
}

.admin-user-avatar i {
    font-size: 40px;
    color: #6c757d;
}

.admin-user-info {
    flex: 1;
}

.admin-user-name {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
}

.admin-user-role {
    font-size: 12px;
    color: #6c757d;
}

.admin-user-dropdown-btn {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.admin-user-dropdown-btn:hover {
    background-color: #e9ecef;
    color: #495057;
}

.admin-user-dropdown {
    background: white;
    border: none;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    border-radius: 8px;
    padding: 8px 0;
}

.admin-user-dropdown .dropdown-item {
    padding: 10px 20px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: background-color 0.3s;
}

.admin-user-dropdown .dropdown-item:hover {
    background-color: #f8f9fa;
}

/* Notifications */
.admin-sidebar-notifications {
    padding: 15px 20px;
    border-bottom: 1px solid #e5e5e5;
    flex-shrink: 0;
}

.admin-notification-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    background: #e3f2fd;
    border-radius: 6px;
    font-size: 14px;
    color: #1976d2;
}

.admin-notification-badge {
    background: #ff4757;
    color: white;
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: auto;
}

/* Navigation - Scrollable Area */
.admin-sidebar-nav {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    min-height: 0;
    display: flex;
    flex-direction: column;
    padding: 10px 0;
}

/* Custom scrollbar for navigation area */
.admin-sidebar-nav::-webkit-scrollbar {
    width: 4px;
}

.admin-sidebar-nav::-webkit-scrollbar-track {
    background: transparent;
}

.admin-sidebar-nav::-webkit-scrollbar-thumb {
    background: #dee2e6;
    border-radius: 2px;
}

.admin-sidebar-nav::-webkit-scrollbar-thumb:hover {
    background: #adb5bd;
}

.admin-nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
    width: 100%;
}

/* Ensure the sidebar has proper flex structure */
.admin-sidebar {
    width: 280px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f1ff 100%);
    color: white;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    transition: transform 0.3s ease;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden; /* Hide outer scrollbar */
}

.admin-nav-item {
    margin-bottom: 2px;
}

.admin-nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #495057;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.admin-nav-link:hover {
    background-color: #f8f9fa;
    color: #007bff;
    text-decoration: none;
}

.admin-nav-link.active {
    background-color: #e3f2fd;
    color: #1976d2;
    border-right: 3px solid #1976d2;
    font-weight: 600;
}

.admin-nav-icon {
    width: 20px;
    margin-right: 12px;
    text-align: center;
}

.admin-nav-text {
    flex: 1;
}

.admin-nav-arrow {
    font-size: 12px;
    transition: transform 0.3s ease;
}

/* Dropdown Navigation */
.admin-nav-dropdown.active .admin-nav-arrow {
    transform: rotate(90deg);
}

.admin-nav-submenu {
    list-style: none;
    padding: 0;
    margin: 0;
    display: none;
    background-color: #f8f9fa;
    border-top: 1px solid #e5e5e5;
}

.admin-nav-dropdown.active .admin-nav-submenu {
    display: block;
}

.admin-nav-sublink {
    display: flex;
    align-items: center;
    padding: 10px 20px 10px 52px;
    color: #6c757d;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
}

.admin-nav-sublink:hover {
    background-color: #e9ecef;
    color: #007bff;
    text-decoration: none;
}

.admin-nav-sublink.active {
    background-color: #e3f2fd;
    color: #1976d2;
    font-weight: 600;
}

.admin-nav-sublink i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
}

/* Sidebar Footer */
.admin-sidebar-footer {
    padding: 20px;
    border-top: 1px solid #e5e5e5;
    flex-shrink: 0;
    background: #f8f9fa;
}

.admin-sidebar-footer-item {
    margin-bottom: 10px;
}

.admin-footer-link {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #6c757d;
    text-decoration: none;
    font-size: 14px;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.admin-footer-link:hover {
    background-color: #e9ecef;
    color: #007bff;
    text-decoration: none;
}

/* Main Content */
.admin-dashboard-content {
    flex: 1;
    margin-left: 280px;
    padding: 20px;
    min-height: 100vh;
}

/* Mobile Styles */
@media (max-width: 991.98px) {
    .admin-sidebar {
        transform: translateX(-100%);
        width: 260px; /* Slightly smaller on mobile */
    }

    .admin-sidebar.active {
        transform: translateX(0);
    }

    .admin-dashboard-content {
        margin-left: 0;
        width: 100%;
    }

    .admin-sidebar-toggle {
        display: block;
        position: fixed;
        top: 15px;
        left: 15px;
        z-index: 999;
        background: #007bff;
        border: none;
        color: white;
        padding: 10px 12px;
        border-radius: 6px;
        font-size: 16px;
        cursor: pointer;
        box-shadow: 0 4px 15px rgba(0,123,255,0.3);
        transition: all 0.3s ease;
    }

    .admin-sidebar-toggle:hover {
        background: #0056b3;
        transform: translateY(-1px);
        box-shadow: 0 6px 20px rgba(0,123,255,0.4);
    }

    /* Prevent body scroll when sidebar is open */
    body.sidebar-open {
        overflow: hidden;
    }
}

/* Desktop Styles */
@media (min-width: 992px) {
    .admin-sidebar-toggle {
        display: none;
    }
}

/* Sidebar Overlay */
.admin-sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.admin-sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Body and layout adjustments */
body {
    margin: 0;
    padding: 0;
}

.admin-dashboard-wrapper {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Ensure proper spacing for topbar */
.admin-topbar {
    position: relative;
    z-index: 998;
    margin-left: 280px;
    transition: margin-left 0.3s ease;
}

@media (max-width: 991.98px) {
    .admin-topbar {
        margin-left: 0;
    }
}
