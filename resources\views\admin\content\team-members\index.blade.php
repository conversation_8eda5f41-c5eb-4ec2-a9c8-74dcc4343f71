@extends('admin.admin-layout')

@section('content')
<div class="row">
    <!-- Creative Header -->
    <div class="col-12">
        <div class="admin-creative-header mb-4">
            <div class="admin-header-content">
                <div class="admin-header-main">
                    <div class="admin-header-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="admin-header-text">
                        <h1 class="admin-header-title">Team Members</h1>
                        <p class="admin-header-subtitle">Manage your restaurant's team members and chefs</p>
                    </div>
                </div>
                <div class="admin-header-actions">
                    <a href="{{ route('admin.team-members.create') }}" class="admin-btn">
                        <i class="fas fa-plus"></i> Add Team Member
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="admin-table-wrapper">
            <div class="admin-table-header">
                <h3 class="admin-table-title">All Team Members</h3>
                <div class="admin-table-actions">
                    <span class="admin-badge admin-badge-info">{{ $members->count() }} Total</span>
                </div>
            </div>
            
            @if($members->count() > 0)
                <div class="row">
                    @foreach($members as $member)
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="admin-team-card">
                            <div class="admin-team-image">
                                <img src="{{ asset('storage/' . $member->image) }}" alt="{{ $member->name }}" class="img-fluid">
                                <div class="admin-team-status">
                                    <div class="admin-toggle-switch">
                                        <input type="checkbox" id="status-{{ $member->id }}" 
                                               {{ $member->is_active ? 'checked' : '' }}
                                               onchange="toggleStatus({{ $member->id }}, '{{ route('admin.team-members.toggle-status', $member) }}')">
                                        <label for="status-{{ $member->id }}"></label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="admin-team-content">
                                <h4 class="admin-team-name">{{ $member->name }}</h4>
                                <p class="admin-team-position">{{ $member->position }}</p>
                                
                                <div class="admin-team-social">
                                    @if($member->facebook_url)
                                        <a href="{{ $member->facebook_url }}" target="_blank" class="admin-social-link facebook">
                                            <i class="fab fa-facebook-f"></i>
                                        </a>
                                    @endif
                                    @if($member->linkedin_url)
                                        <a href="{{ $member->linkedin_url }}" target="_blank" class="admin-social-link linkedin">
                                            <i class="fab fa-linkedin-in"></i>
                                        </a>
                                    @endif
                                    @if($member->twitter_url)
                                        <a href="{{ $member->twitter_url }}" target="_blank" class="admin-social-link twitter">
                                            <i class="fab fa-twitter"></i>
                                        </a>
                                    @endif
                                    @if($member->instagram_url)
                                        <a href="{{ $member->instagram_url }}" target="_blank" class="admin-social-link instagram">
                                            <i class="fab fa-instagram"></i>
                                        </a>
                                    @endif
                                </div>
                                
                                <div class="admin-team-meta">
                                    <span class="admin-badge admin-badge-secondary">Order: {{ $member->sort_order }}</span>
                                </div>
                                
                                <div class="admin-team-actions">
                                    <a href="{{ route('admin.team-members.show', $member) }}" class="admin-btn admin-btn-sm admin-btn-info">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    <a href="{{ route('admin.team-members.edit', $member) }}" class="admin-btn admin-btn-sm admin-btn-warning">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    <form action="{{ route('admin.team-members.destroy', $member) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="admin-btn admin-btn-sm admin-btn-danger" 
                                                onclick="return confirm('Are you sure you want to delete this team member?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            @else
                <div class="admin-empty-state">
                    <div class="admin-empty-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3>No Team Members Found</h3>
                    <p>Add your first team member to showcase your restaurant's talented staff.</p>
                    <a href="{{ route('admin.team-members.create') }}" class="admin-btn">
                        <i class="fas fa-plus"></i> Add First Member
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<style>
.admin-team-card {
    background: var(--cardBg);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
}

.admin-team-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.admin-team-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.admin-team-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.admin-team-status {
    position: absolute;
    top: 15px;
    right: 15px;
}

.admin-team-content {
    padding: 20px;
    text-align: center;
}

.admin-team-name {
    font-size: 20px;
    font-weight: 600;
    color: var(--headingColor);
    margin-bottom: 5px;
}

.admin-team-position {
    color: var(--primaryColor);
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 15px;
}

.admin-team-social {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 15px;
}

.admin-social-link {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: transform 0.3s ease;
}

.admin-social-link:hover {
    transform: scale(1.1);
    color: white;
}

.admin-social-link.facebook { background: #3b5998; }
.admin-social-link.linkedin { background: #0077b5; }
.admin-social-link.twitter { background: #1da1f2; }
.admin-social-link.instagram { background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888); }

.admin-team-meta {
    margin-bottom: 15px;
}

.admin-team-actions {
    display: flex;
    gap: 8px;
}

.admin-team-actions .admin-btn {
    flex: 1;
    text-align: center;
}
</style>

<script>
function toggleStatus(id, url) {
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}
</script>
@endsection
